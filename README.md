# 中国股指期货报告自动生成系统

**版本**: MVP-V1.0
**状态**: 开发中

## 📋 项目概览

这是一个专为期货分析师和量化研究员设计的股指期货报告自动生成工具，能够从数据获取到报告生成实现完整的自动化流程。

### 🎯 核心功能

1. **量化图表生成模块** - 自动连接Wind API，生成12张专业图表和量化分析洞察
2. **舆情文章获取模块** - 半自动化获取微信公众号文章内容
3. **报告编排生成模块** - 使用AI整合所有信息，生成专业的期货分析报告

### 🛠️ 技术栈

- **Python 3.8+** - 核心开发语言
- **WindPy** - Wind金融数据接口
- **Selenium** - 浏览器自动化
- **OpenAI GPT-4** - AI报告生成
- **Plotly/Matplotlib** - 专业图表生成

## 🚀 快速开始

### 环境要求

1. Python 3.8或更高版本
2. Wind终端（需要有效的Wind账户）
3. OpenAI API密钥
4. Chrome浏览器

### 安装依赖

```bash
pip install -r requirements.txt
```

### 配置设置

1. 复制 `config/config_template.py` 为 `config/config.py`
2. 填入您的API密钥和配置信息
3. 确保Wind终端已启动并登录

### 运行程序

```bash
python main.py
```

## 📁 项目结构

```
my_report_generator/
├── main.py                    # 主程序入口
├── config/                    # 配置文件目录
│   ├── config.py             # 主配置文件
│   └── system_rules.yaml     # 系统规则配置
├── modules/                   # 功能模块
│   ├── chart_generator.py     # 图表生成模块
│   ├── article_fetcher.py     # 文章获取模块
│   └── report_composer.py     # 报告编排模块
├── output/                    # 输出目录
│   ├── images/               # 图表文件
│   ├── articles/             # 舆情文章
│   └── reports/              # 最终报告
├── templates/                 # 模板文件
├── utils/                     # 工具函数
└── tests/                     # 测试文件
```

## 📊 输出示例

系统将生成以下输出：

1. **12张专业图表** (HTML/PNG格式)
   - 市场情绪面板
   - 两融规模图
   - 波动率核心指标表
   - 股指期货价格走势图
   - IV-HV走势图
   - PCR走势图
   - 微笑曲线
   - 波动率期限结构
   - 波动率曲面
   - IV-HV价差图
   - 期权持仓分布
   - 北向/南向资金流

2. **舆情文章摘要** (TXT格式)
3. **完整分析报告** (Markdown格式)
   - 晨会口语分享稿（5分钟）
   - 详细策略报告

## ⚠️ 注意事项

1. 首次运行时需要手动登录微信公众平台
2. 确保Wind终端在运行期间保持连接
3. 建议在交易时间外运行以获得最佳性能
4. 生成的报告仅供参考，不构成投资建议

## 📝 更新日志

### v1.0.0 (开发中)
- 初始版本开发
- 基础框架搭建
- 核心模块实现

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 📄 许可证

本项目仅供内部使用，请勿外传。
用于存放研报输出系统相关代码和文件
