#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
目录管理工具
"""

import os
from pathlib import Path
from loguru import logger


def ensure_directories():
    """确保所有必要的目录存在"""
    
    # 需要创建的目录列表
    directories = [
        "output",
        "output/images",
        "output/articles",
        "output/reports",
        "output/temp",
        "logs",
        "templates",
        "modules",
        "utils",
        "tests",
        "input"
    ]

    created_dirs = []
    
    for dir_path in directories:
        path = Path(dir_path)
        if not path.exists():
            path.mkdir(parents=True, exist_ok=True)
            created_dirs.append(dir_path)
            logger.debug(f"创建目录: {dir_path}")
    
    if created_dirs:
        logger.info(f"创建了 {len(created_dirs)} 个目录")
    else:
        logger.debug("所有目录已存在")
    
    return True


def clean_temp_directory():
    """清理临时目录"""
    temp_dir = Path("output/temp")
    if temp_dir.exists():
        for file in temp_dir.glob("*"):
            if file.is_file():
                file.unlink()
                logger.debug(f"删除临时文件: {file}")
    
    logger.info("临时目录清理完成")


def get_output_path(file_type, filename):
    """获取输出文件的完整路径"""
    
    type_mapping = {
        "image": "output/images",
        "article": "output/articles", 
        "report": "output/reports",
        "temp": "output/temp"
    }
    
    if file_type not in type_mapping:
        raise ValueError(f"不支持的文件类型: {file_type}")
    
    base_dir = Path(type_mapping[file_type])
    base_dir.mkdir(parents=True, exist_ok=True)
    
    return base_dir / filename


def list_output_files():
    """列出所有输出文件"""
    
    output_files = {
        "images": [],
        "articles": [],
        "reports": []
    }
    
    # 扫描图表文件
    images_dir = Path("output/images")
    if images_dir.exists():
        output_files["images"] = list(images_dir.glob("*"))
    
    # 扫描文章文件
    articles_dir = Path("output/articles")
    if articles_dir.exists():
        output_files["articles"] = list(articles_dir.glob("*.txt"))
    
    # 扫描报告文件
    reports_dir = Path("output/reports")
    if reports_dir.exists():
        output_files["reports"] = list(reports_dir.glob("*.md"))
    
    return output_files
