#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Gemini API集成测试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_gemini_import():
    """测试Gemini API导入"""
    print("🔍 测试Gemini API导入...")
    
    try:
        import google.generativeai as genai
        print("✅ google.generativeai导入成功")
        return True
    except ImportError as e:
        print(f"❌ google.generativeai导入失败: {e}")
        print("请安装: pip install google-generativeai")
        return False

def test_gemini_connection():
    """测试Gemini连接"""
    print("🔗 测试Gemini连接...")
    
    try:
        import google.generativeai as genai
        from config.config import GEMINI_CONFIG
        
        # 配置API密钥
        genai.configure(api_key=GEMINI_CONFIG["api_key"])
        
        # 测试模型列表
        models = genai.list_models()
        available_models = [m.name for m in models if 'generateContent' in m.supported_generation_methods]
        
        print(f"可用模型: {available_models[:3]}...")  # 显示前3个
        print("✅ Gemini API连接成功")
        return True
        
    except Exception as e:
        print(f"❌ Gemini连接失败: {e}")
        return False

def test_gemini_generation():
    """测试Gemini文本生成"""
    print("📝 测试Gemini文本生成...")
    
    try:
        import google.generativeai as genai
        from config.config import GEMINI_CONFIG
        
        # 配置API密钥
        genai.configure(api_key=GEMINI_CONFIG["api_key"])
        
        # 创建模型
        model = genai.GenerativeModel('gemini-1.5-pro')
        
        # 测试生成
        prompt = "请简要介绍中国股指期货市场的三大主要品种：IH、IF、IM。"
        response = model.generate_content(prompt)
        
        print("生成内容预览:")
        print(response.text[:200] + "..." if len(response.text) > 200 else response.text)
        print("✅ Gemini文本生成成功")
        return True
        
    except Exception as e:
        print(f"❌ Gemini文本生成失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Gemini API集成测试")
    print("=" * 50)
    
    tests = [
        ("Gemini API导入", test_gemini_import),
        ("Gemini连接", test_gemini_connection),
        ("Gemini文本生成", test_gemini_generation)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
            print()
    
    # 汇总结果
    print("=" * 50)
    print("📋 测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！Gemini API集成成功！")
        print("\n💡 现在可以运行完整系统:")
        print("   python main.py")
    else:
        print("⚠️ 部分测试失败，请检查配置")

if __name__ == "__main__":
    main()
