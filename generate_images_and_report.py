#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成PNG图片并创建包含图片的完整报告
"""

import sys
import os
from pathlib import Path
import base64
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def convert_html_to_png():
    """将HTML图表转换为PNG图片"""
    print("🖼️ 正在生成PNG图片...")
    
    try:
        from modules.chart_generator import ChartGenerator
        import plotly.io as pio
        
        # 创建图表生成器
        generator = ChartGenerator()
        
        # 图表列表
        charts = [
            ("market_sentiment_panel", "市场情绪面板", generator.generate_market_sentiment_panel),
            ("futures_price_trend", "期货价格走势图", generator.generate_futures_price_trend),
            ("iv_hv_trend", "IV-HV走势图", generator.generate_iv_hv_trend),
            ("pcr_trend", "PCR走势图", generator.generate_pcr_trend),
            ("volatility_surface", "波动率曲面图", generator.generate_volatility_surface),
            ("capital_flow", "资金流向图", generator.generate_capital_flow),
            ("margin_trading_scale", "两融规模图", generator.generate_margin_trading_scale)
        ]
        
        # 确保PNG输出目录存在
        png_dir = Path("output/images/png")
        png_dir.mkdir(exist_ok=True)
        
        generated_images = []
        
        for chart_name, chart_title, chart_func in charts:
            try:
                print(f"正在生成 {chart_title}...")
                
                # 生成图表并获取plotly figure对象
                fig = chart_func(return_fig=True)  # 假设我们修改函数返回figure对象
                
                if fig:
                    # 保存为PNG
                    png_path = png_dir / f"{chart_name}.png"
                    pio.write_image(fig, str(png_path), width=1200, height=800, scale=2)
                    generated_images.append((chart_name, chart_title, str(png_path)))
                    print(f"✅ {chart_title} PNG已保存")
                
            except Exception as e:
                print(f"❌ 生成 {chart_title} PNG失败: {e}")
        
        return generated_images
        
    except Exception as e:
        print(f"❌ PNG生成失败: {e}")
        return []

def create_report_with_images():
    """创建包含图片的完整报告"""
    print("\n📝 正在创建包含图片的报告...")
    
    try:
        # 读取最新的报告
        reports_dir = Path("output/reports")
        report_files = list(reports_dir.glob("daily_report_*.md"))
        
        if not report_files:
            print("❌ 未找到报告文件")
            return False
        
        # 获取最新报告
        latest_report = max(report_files, key=lambda x: x.stat().st_mtime)
        
        with open(latest_report, 'r', encoding='utf-8') as f:
            report_content = f.read()
        
        # 创建新的报告内容
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        new_report_path = reports_dir / f"complete_report_with_images_{timestamp}.md"
        
        # 图表信息
        chart_info = [
            ("market_sentiment_panel", "市场情绪面板", "综合市场指标展示"),
            ("futures_price_trend", "期货价格走势图", "三大股指期货价格对比"),
            ("iv_hv_trend", "IV-HV走势图", "隐含波动率vs历史波动率分析"),
            ("pcr_trend", "PCR走势图", "Put/Call比率情绪指标"),
            ("volatility_surface", "波动率曲面图", "3D波动率微笑曲线"),
            ("capital_flow", "资金流向图", "北向/南向资金流动分析"),
            ("margin_trading_scale", "两融规模图", "融资融券余额变化趋势")
        ]
        
        # 构建新报告内容
        new_content = f"""# 股指期货与期权完整分析报告 (含图表)

**生成时间**: {datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")}  
**数据源**: Wind金融终端 + AI智能分析

---

## 📊 专业图表展示

"""
        
        # 添加每个图表的说明和链接
        for i, (chart_name, chart_title, description) in enumerate(chart_info, 1):
            html_path = f"images/{chart_name}.html"
            png_path = f"images/png/{chart_name}.png"
            
            new_content += f"""### {i}. {chart_title}

**功能说明**: {description}

**查看方式**:
- 🌐 **交互式图表**: [点击打开HTML版本]({html_path}) (支持缩放、悬停查看数据)
- 🖼️ **静态图片**: [点击查看PNG版本]({png_path}) (适合打印和分享)

**文件位置**: 
- HTML: `output/{html_path}`
- PNG: `output/{png_path}`

---

"""
        
        # 添加原报告内容
        new_content += f"""## 📋 详细分析报告

{report_content}

---

## 📁 文件说明

### 图表文件位置
```
output/
├── images/                    # 图表文件夹
│   ├── *.html                # 交互式HTML图表 (推荐)
│   └── png/                  # PNG静态图片
│       └── *.png            # 静态图片文件
├── articles/                 # 舆情文章
│   └── *.txt                # 获取的文章内容
└── reports/                  # 分析报告
    └── *.md                 # Markdown格式报告
```

### 如何查看图表

#### 方法1: 使用图表查看器 (推荐)
```bash
python view_charts.py
```

#### 方法2: 直接打开HTML文件
1. 进入 `output/images/` 文件夹
2. 双击任意 `.html` 文件
3. 在浏览器中查看交互式图表

#### 方法3: 查看PNG图片
1. 进入 `output/images/png/` 文件夹
2. 双击任意 `.png` 文件
3. 用图片查看器打开

### 💡 使用建议

- **日常分析**: 使用HTML交互式图表，可以缩放和查看详细数据
- **报告分享**: 使用PNG图片，方便插入PPT或邮件
- **打印输出**: PNG格式适合打印成纸质版本

---

*本报告由中国股指期货报告自动生成系统生成*  
*系统版本: v1.0.0 | 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}*
"""
        
        # 保存新报告
        with open(new_report_path, 'w', encoding='utf-8') as f:
            f.write(new_content)
        
        print(f"✅ 完整报告已生成: {new_report_path}")
        return str(new_report_path)
        
    except Exception as e:
        print(f"❌ 报告生成失败: {e}")
        return None

def show_file_locations():
    """显示文件位置指南"""
    print("\n" + "="*60)
    print("📁 您的图表文件位置指南")
    print("="*60)
    
    # 获取绝对路径
    project_path = Path(__file__).parent.absolute()
    images_path = project_path / "output" / "images"
    
    print(f"\n🖼️ 图表文件位置:")
    print(f"   完整路径: {images_path}")
    print(f"   相对路径: output/images/")
    
    print(f"\n📊 可用的图表文件:")
    if images_path.exists():
        for i, html_file in enumerate(images_path.glob("*.html"), 1):
            print(f"   {i}. {html_file.name}")
    
    print(f"\n💡 查看图表的3种方法:")
    print(f"   1. 运行: python view_charts.py")
    print(f"   2. 双击HTML文件用浏览器打开")
    print(f"   3. 在Finder中打开: {images_path}")
    
    print(f"\n🔍 在Finder中快速打开:")
    print(f"   1. 按 Cmd+Space 打开Spotlight搜索")
    print(f"   2. 输入: {images_path}")
    print(f"   3. 按回车打开文件夹")

def main():
    """主函数"""
    print("🎨 图表文件处理和报告生成工具")
    print("="*50)
    
    # 显示文件位置
    show_file_locations()
    
    # 询问是否生成PNG图片
    print(f"\n❓ 是否需要生成PNG格式的图片？")
    print(f"   (HTML图表更好用，但PNG适合插入文档)")
    
    choice = input("输入 y 生成PNG图片，直接回车跳过: ").strip().lower()
    
    if choice == 'y':
        # 生成PNG图片
        images = convert_html_to_png()
        if images:
            print(f"✅ 成功生成 {len(images)} 张PNG图片")
    
    # 创建完整报告
    report_path = create_report_with_images()
    
    if report_path:
        print(f"\n🎉 完成！您的文件位置:")
        print(f"   📊 图表: output/images/")
        print(f"   📋 报告: {report_path}")
        
        # 询问是否打开文件夹
        choice = input(f"\n是否打开图表文件夹？(y/n): ").strip().lower()
        if choice == 'y':
            import subprocess
            images_path = Path("output/images").absolute()
            subprocess.run(["open", str(images_path)])

if __name__ == "__main__":
    main()
