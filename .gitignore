# 中国股指期货报告自动生成系统 - Git忽略文件
# 创建时间: 2025-08-03

# Python相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
venv/
env/
ENV/
.venv/
.env/

# IDE和编辑器
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# 日志文件
logs/*.log
*.log

# 输出文件（动态生成的内容）
output/temp/
output/cache/
output/images/*.html
output/articles/*.txt
output/reports/*_202*.html
output/reports/*_202*.md
*.tmp
*.temp

# Windows 脚本文件
*.bat
*.ps1
run_*.bat
run_*.ps1

# 配置文件中的敏感信息
config/config.py
.env
*.key
*.secret

# Wind API相关
WindPy/
wind_data_cache/

# 测试覆盖率报告
htmlcov/
.coverage
.pytest_cache/

# 系统文件
.DS_Store
Thumbs.db
desktop.ini

# 临时文件
*.bak
*.backup
*~

# 大文件和数据文件（根据需要调整）
*.xlsx
*.xls
*.csv
data/raw/
data/cache/

# 图片缓存（保留重要图片，忽略临时图片）
output/images/temp/
output/images/cache/
