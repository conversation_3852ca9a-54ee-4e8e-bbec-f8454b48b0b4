#!/usr/bin/env python3
"""
测试微信文章导出工具API集成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.wxdown_api_client import WxDownAPIClient
from modules.report_composer import ReportComposer
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')
logger = logging.getLogger(__name__)

def test_api_client():
    """测试API客户端"""
    print("🔍 测试微信文章导出工具API客户端")
    print("=" * 60)
    
    try:
        # 初始化API客户端
        print("📱 初始化API客户端...")
        client = WxDownAPIClient()
        
        print(f"✓ 配置加载成功")
        print(f"  - 关键词数量: {len(client.keywords)}")
        print(f"  - 时间窗: {client.time_window[0].strftime('%Y-%m-%d %H:%M')} -> {client.time_window[1].strftime('%Y-%m-%d %H:%M')}")
        
        # 获取文章
        print(f"\n🔄 开始自动获取文章...")
        articles = client.get_articles_auto()
        
        if articles:
            print(f"✅ 成功获取 {len(articles)} 篇文章")
            
            # 显示文章列表
            print(f"\n📋 文章列表:")
            for i, article in enumerate(articles[:10], 1):
                title = article.get('title', 'Unknown')
                source = article.get('source', 'Unknown')
                pub_time = article.get('publish_time', 'Unknown')
                url = article.get('url', '')
                
                print(f"  {i}. {source}《{title[:40]}...》")
                print(f"     时间: {pub_time}")
                print(f"     URL: {url[:60]}...")
                print()
            
            if len(articles) > 10:
                print(f"  ... 还有 {len(articles) - 10} 篇文章")
            
            return articles
        else:
            print("⚠️  未获取到任何文章")
            print("可能原因:")
            print("1. 时间窗内没有新文章")
            print("2. API接口访问限制")
            print("3. 网络连接问题")
            print("4. 关键词过滤过于严格")
            
            return []
            
    except Exception as e:
        print(f"❌ API客户端测试失败: {e}")
        logger.exception("详细错误信息:")
        return []

def test_report_generation(articles):
    """测试报告生成"""
    if not articles:
        print("\n⚠️  没有文章数据，跳过报告生成测试")
        return None
    
    print(f"\n📝 测试报告生成...")
    print("=" * 60)
    
    try:
        # 初始化报告编排器
        report_composer = ReportComposer()
        
        # 准备数据
        article_data = {'wechat': articles}
        chart_data = {}  # 暂时跳过图表生成
        
        # 生成报告
        print("🔄 正在生成报告...")
        report_path = report_composer.compose_report(
            chart_data=chart_data,
            article_data=article_data
        )
        
        if report_path:
            print(f"✅ 报告生成成功: {report_path}")
            
            # 显示报告统计
            try:
                with open(report_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    word_count = len(content)
                
                print(f"📊 报告统计:")
                print(f"  - 文件路径: {report_path}")
                print(f"  - 字数: {word_count}")
                print(f"  - 基于文章数: {len(articles)}")
                print(f"  - 数据源: 微信文章导出工具API")
                print(f"  - 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                
                # 检查报告质量
                quality_score = assess_report_quality(content, articles)
                print(f"  - 质量评分: {quality_score}/100")
                
                return report_path
                
            except Exception as e:
                print(f"⚠️  无法读取报告统计: {e}")
                return report_path
        else:
            print("❌ 报告生成失败")
            return None
            
    except Exception as e:
        print(f"❌ 报告生成测试失败: {e}")
        logger.exception("详细错误信息:")
        return None

def assess_report_quality(content, articles):
    """评估报告质量"""
    score = 0
    
    # 基础结构检查 (30分)
    if "晨会口语分享稿" in content:
        score += 10
    if "详细策略报告" in content:
        score += 10
    if "附录" in content:
        score += 10
    
    # 内容丰富度检查 (40分)
    if len(content) > 2000:
        score += 10
    if len(content) > 3000:
        score += 10
    if "权重" in content:
        score += 10
    if "风险" in content:
        score += 10
    
    # 数据完整性检查 (30分)
    article_count = len(articles)
    if article_count >= 5:
        score += 10
    if article_count >= 10:
        score += 10
    if any("期权" in article.get('title', '') or "股指" in article.get('title', '') for article in articles):
        score += 10
    
    return min(score, 100)

def test_end_to_end():
    """端到端测试"""
    print("🎯 微信文章导出工具API集成 - 端到端测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 步骤1: 测试API客户端
    articles = test_api_client()
    
    # 步骤2: 测试报告生成
    report_path = test_report_generation(articles)
    
    # 总结
    print(f"\n🎉 端到端测试完成")
    print("=" * 60)
    
    if articles and report_path:
        print("✅ 所有测试通过！")
        print(f"📊 成果:")
        print(f"  - 获取文章: {len(articles)} 篇")
        print(f"  - 生成报告: {report_path}")
        print(f"  - 数据源: 微信文章导出工具API")
        print(f"  - 抓取方式: 完全自动化（无需浏览器）")
        
        print(f"\n💡 使用方法:")
        print(f"  python main.py")
        print(f"  选择第二通道 -> 选项2（微信文章导出工具）")
        
        return True
    else:
        print("⚠️  部分测试失败")
        if not articles:
            print("  - 文章获取失败")
        if not report_path:
            print("  - 报告生成失败")
        
        print(f"\n💡 备选方案:")
        print(f"  1. 检查网络连接")
        print(f"  2. 手动收集文章URL到 input/article_urls.txt")
        print(f"  3. 运行 python scripts/validate_plan_b.py")
        
        return False

def main():
    """主函数"""
    success = test_end_to_end()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
