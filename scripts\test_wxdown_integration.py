#!/usr/bin/env python3
"""
测试微信文章导出工具集成
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.wxdown_fetcher import WxDownFetcher
from modules.report_composer import ReportComposer
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')
logger = logging.getLogger(__name__)

def main():
    """测试微信文章导出工具集成"""
    print("🔧 微信文章导出工具集成测试")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    print("📋 测试流程：")
    print("1. 打开微信文章导出工具网站")
    print("2. 手动配置公众号监控")
    print("3. 尝试获取文章列表")
    print("4. 生成测试报告")
    print("=" * 60)
    
    # 询问用户是否继续
    confirm = input("\n是否继续测试微信文章导出工具? (y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 用户取消测试")
        return False
    
    fetcher = None
    try:
        # 初始化微信文章导出工具抓取器
        print("\n📱 初始化微信文章导出工具抓取器...")
        fetcher = WxDownFetcher(headless=False)
        
        # 运行抓取
        print("\n🔄 开始抓取流程...")
        articles = fetcher.run()
        
        if articles:
            print(f"\n✅ 抓取成功！获取到 {len(articles)} 篇文章")
            
            # 显示文章列表
            print("\n📋 文章列表:")
            for i, article in enumerate(articles[:10], 1):
                title = article.get('title', 'Unknown')
                source = article.get('source', 'Unknown')
                url = article.get('url', '')
                print(f"  {i}. {source}《{title[:40]}...》")
                if url:
                    print(f"     URL: {url[:60]}...")
            
            if len(articles) > 10:
                print(f"  ... 还有 {len(articles) - 10} 篇文章")
            
            # 生成测试报告
            print(f"\n📝 生成基于微信文章导出工具的测试报告...")
            
            report_composer = ReportComposer()
            article_data = {'wechat': articles}
            chart_data = {}
            
            report_path = report_composer.compose_report(
                chart_data=chart_data,
                article_data=article_data
            )
            
            if report_path:
                print(f"✅ 报告生成成功: {report_path}")
                
                # 显示报告统计
                try:
                    with open(report_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        word_count = len(content)
                    print(f"📊 报告统计:")
                    print(f"  - 字数: {word_count}")
                    print(f"  - 基于文章数: {len(articles)}")
                    print(f"  - 数据源: 微信文章导出工具")
                    print(f"  - 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                except Exception as e:
                    print(f"⚠️  无法读取报告统计: {e}")
            else:
                print("❌ 报告生成失败")
                return False
            
            print("\n🎉 微信文章导出工具集成测试完成！")
            return True
            
        else:
            print("⚠️  未获取到任何文章")
            print("\n💡 可能的解决方案：")
            print("1. 确保在网站中正确配置了公众号")
            print("2. 检查公众号是否有最新文章")
            print("3. 尝试手动导出文章URL到 input/article_urls.txt")
            print("4. 然后运行 python scripts/validate_plan_b.py")
            
            return False
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        return False
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        logger.exception("详细错误信息:")
        return False
    finally:
        # 清理资源
        if fetcher:
            try:
                fetcher.cleanup()
            except:
                pass

def show_usage_guide():
    """显示使用指南"""
    print("\n" + "="*60)
    print("📖 微信文章导出工具使用指南")
    print("="*60)
    print("1. 网站地址: https://exporter.wxdown.online/dashboard/account")
    print("2. 功能特点:")
    print("   - 无需处理验证码")
    print("   - 支持批量监控公众号")
    print("   - 可导出文章数据")
    print("   - 更稳定的数据获取")
    print("3. 使用步骤:")
    print("   - 注册/登录账号")
    print("   - 添加要监控的公众号")
    print("   - 设置导出参数")
    print("   - 获取文章数据")
    print("4. 集成到系统:")
    print("   - 运行: python main.py")
    print("   - 选择第二通道 -> 选项2")
    print("   - 按提示操作即可")
    print("="*60)

if __name__ == "__main__":
    show_usage_guide()
    success = main()
    print(f"\n🎯 测试结果: {'成功' if success else '失败'}")
    
    if not success:
        print("\n💡 备选方案:")
        print("1. 手动从网站导出文章URL")
        print("2. 添加到 input/article_urls.txt")
        print("3. 运行 python scripts/validate_plan_b.py")
    
    sys.exit(0 if success else 1)
