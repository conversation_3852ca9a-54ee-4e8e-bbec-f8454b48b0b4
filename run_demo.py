#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速演示脚本
用于演示系统的基本功能，无需完整配置
"""

import os
import sys
import time
import webbrowser
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def check_dependencies():
    """检查依赖是否安装"""
    required_packages = [
        'pandas', 'numpy', 'plotly', 'matplotlib',
        'selenium', 'loguru'
    ]

    # 特殊处理的包（导入名称与包名不同）
    special_packages = {
        'bs4': 'beautifulsoup4'
    }
    
    missing_packages = []

    # 检查普通包
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)

    # 检查特殊包
    for import_name, package_name in special_packages.items():
        try:
            __import__(import_name)
        except ImportError:
            missing_packages.append(package_name)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for pkg in missing_packages:
            print(f"   - {pkg}")
        print("\n请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 所有依赖包已安装")
    return True

def run_demo():
    """运行演示"""
    print("🚀 中国股指期货报告自动生成系统 - 演示模式")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    try:
        # 导入模块
        from utils.directory_manager import ensure_directories
        from utils.logger_setup import setup_logger
        from modules.chart_generator import ChartGenerator
        from modules.article_fetcher import ArticleFetcher
        from modules.report_composer import ReportComposer
        
        # 设置环境
        print("\n📁 设置输出目录...")
        ensure_directories()
        
        print("📝 设置日志系统...")
        logger = setup_logger()
        
        # 演示图表生成
        print("\n📊 演示图表生成功能...")
        chart_generator = ChartGenerator()
        chart_results = chart_generator.generate_all_charts()
        
        if chart_results:
            print(f"✅ 成功生成 {len(chart_results)} 张图表")
        else:
            print("⚠️  图表生成失败")
        
        # 演示文章获取
        print("\n📰 演示文章获取功能...")
        article_fetcher = ArticleFetcher()
        
        # 跳过实际的浏览器操作，直接使用模拟数据
        print("使用模拟数据演示...")
        article_results = article_fetcher.simulate_article_fetching()
        
        if article_results:
            print(f"✅ 成功获取 {len(article_results)} 篇文章")
        else:
            print("⚠️  文章获取失败")
        
        # 演示报告生成
        print("\n📋 演示报告生成功能...")
        report_composer = ReportComposer()
        
        # 使用模拟数据生成报告
        report_result = report_composer.compose_report(
            chart_data=chart_results or {},
            article_data={'wechat': article_results or []}
        )
        
        if report_result:
            print(f"✅ 成功生成报告: {report_result['filename']}")
        else:
            print("⚠️  报告生成失败")
        
        # 显示输出摘要
        print("\n" + "=" * 60)
        print("🎉 演示完成！")
        print("\n📁 输出文件位置:")
        
        output_base = Path("output")
        if output_base.exists():
            for subdir in ["images", "articles", "reports"]:
                subdir_path = output_base / subdir
                if subdir_path.exists():
                    files = list(subdir_path.glob("*"))
                    print(f"   📂 {subdir}: {len(files)} 个文件")
        
        print(f"\n💡 查看输出目录: {output_base.absolute()}")
        print("💡 运行完整版本: python main.py")

        # 询问是否打开图表
        open_charts = input("\n🌐 是否打开生成的图表？(y/n，默认y): ").strip().lower()
        if open_charts != 'n':
            open_demo_charts()

        return True
        
    except Exception as e:
        print(f"\n❌ 演示过程中出错: {e}")
        return False

def open_demo_charts():
    """打开演示生成的图表"""
    try:
        images_dir = Path("output/images")
        if not images_dir.exists():
            print("❌ 图表目录不存在")
            return

        chart_files = list(images_dir.glob("*.html"))
        if not chart_files:
            print("❌ 未找到图表文件")
            return

        print(f"\n🌐 正在打开 {len(chart_files)} 个图表...")

        # 打开前3个图表作为演示
        for i, chart_file in enumerate(chart_files[:3]):
            try:
                file_url = f"file://{chart_file.absolute()}"
                webbrowser.open(file_url)
                chart_name = chart_file.stem.replace('_', ' ').title()
                print(f"✅ 已打开: {chart_name}")
                time.sleep(1)  # 间隔1秒避免同时打开太多
            except Exception as e:
                print(f"❌ 打开 {chart_file.name} 失败: {e}")

        if len(chart_files) > 3:
            print(f"\n💡 还有 {len(chart_files) - 3} 个图表，可运行以下命令查看:")
            print("   python view_charts.py")

    except Exception as e:
        print(f"❌ 打开图表失败: {e}")

if __name__ == "__main__":
    success = run_demo()
    if not success:
        sys.exit(1)
