#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图表查看器
用于在浏览器中查看生成的图表
"""

import os
import sys
import webbrowser
from pathlib import Path

def list_charts():
    """列出所有可用的图表"""
    charts_dir = Path("output/images")
    
    if not charts_dir.exists():
        print("❌ 图表目录不存在，请先运行 python run_demo.py 生成图表")
        return []
    
    chart_files = list(charts_dir.glob("*.html"))
    
    if not chart_files:
        print("❌ 没有找到图表文件，请先运行 python run_demo.py 生成图表")
        return []
    
    print("📊 可用图表列表:")
    print("=" * 50)
    
    chart_info = {
        "market_sentiment_panel.html": "市场情绪面板",
        "futures_price_trend.html": "期货价格走势图", 
        "iv_hv_trend.html": "IV-HV走势图",
        "pcr_trend.html": "PCR走势图",
        "volatility_surface.html": "波动率曲面图",
        "capital_flow.html": "资金流向图",
        "margin_trading_scale.html": "两融规模图"
    }
    
    available_charts = []
    for i, chart_file in enumerate(sorted(chart_files), 1):
        chart_name = chart_file.name
        chart_title = chart_info.get(chart_name, chart_name)
        print(f"{i}. {chart_title} ({chart_name})")
        available_charts.append((chart_file, chart_title))
    
    return available_charts

def open_chart(chart_path):
    """在浏览器中打开图表"""
    try:
        # 获取绝对路径
        abs_path = chart_path.absolute()
        file_url = f"file://{abs_path}"
        
        print(f"🌐 正在浏览器中打开: {chart_path.name}")
        webbrowser.open(file_url)
        return True
        
    except Exception as e:
        print(f"❌ 打开图表失败: {e}")
        return False

def open_all_charts():
    """在浏览器中打开所有图表"""
    charts = list_charts()
    
    if not charts:
        return False
    
    print(f"\n🌐 正在浏览器中打开所有 {len(charts)} 张图表...")
    
    for chart_path, chart_title in charts:
        open_chart(chart_path)
    
    print("✅ 所有图表已在浏览器中打开")
    return True

def interactive_mode():
    """交互模式"""
    while True:
        print("\n" + "=" * 60)
        print("📊 中国股指期货报告系统 - 图表查看器")
        print("=" * 60)
        
        charts = list_charts()
        
        if not charts:
            break
        
        print(f"\n{len(charts) + 2}. 打开所有图表")
        print(f"{len(charts) + 3}. 退出")
        
        try:
            choice = input(f"\n请选择要查看的图表 (1-{len(charts) + 3}): ").strip()
            
            if not choice:
                continue
                
            choice_num = int(choice)
            
            if 1 <= choice_num <= len(charts):
                chart_path, chart_title = charts[choice_num - 1]
                open_chart(chart_path)
                
            elif choice_num == len(charts) + 2:
                open_all_charts()
                
            elif choice_num == len(charts) + 3:
                print("👋 再见！")
                break
                
            else:
                print("❌ 无效选择，请重新输入")
                
        except ValueError:
            print("❌ 请输入有效的数字")
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break

def main():
    """主函数"""
    if len(sys.argv) > 1:
        if sys.argv[1] == "--all":
            # 打开所有图表
            open_all_charts()
        elif sys.argv[1] == "--list":
            # 只列出图表
            list_charts()
        else:
            print("用法:")
            print("  python view_charts.py          # 交互模式")
            print("  python view_charts.py --all    # 打开所有图表")
            print("  python view_charts.py --list   # 列出所有图表")
    else:
        # 交互模式
        interactive_mode()

if __name__ == "__main__":
    main()
