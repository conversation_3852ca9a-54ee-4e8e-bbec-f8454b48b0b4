#!/usr/bin/env python3
"""
测试扩大时间窗的置顶公众号抓取
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.article_fetcher import ArticleFetcher
from modules.wechat_fetcher import WeChatFetcher
from modules.report_composer import ReportComposer
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')
logger = logging.getLogger(__name__)

def main():
    """扩大时间窗测试置顶公众号抓取"""
    print("🔍 扩大时间窗的置顶公众号抓取测试")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 创建自定义时间窗的WeChatFetcher
        print("📅 设置扩大的时间窗（过去24小时）...")
        
        # 手动设置时间窗为过去24小时
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)
        
        print(f"时间窗: {start_time.strftime('%Y-%m-%d %H:%M')} → {end_time.strftime('%Y-%m-%d %H:%M')}")
        
        # 初始化WeChatFetcher并手动设置时间窗
        fetcher = WeChatFetcher(headless=False)
        fetcher.time_window = (start_time, end_time)
        
        print(f"\n📱 开始抓取前3个公众号（测试用）...")
        
        # 只测试前3个公众号
        test_accounts = ["华泰期货研究院", "银河期货", "国都期权"]
        
        all_articles = []
        for i, account in enumerate(test_accounts, 1):
            print(f"\n🔄 [{i}/3] 抓取: {account}")
            
            try:
                # 发现文章URL
                urls = fetcher.discover_article_urls(account)
                print(f"  发现 {len(urls)} 个URL")
                
                if urls:
                    # 抓取前2篇文章（避免过多）
                    for j, url in enumerate(urls[:2], 1):
                        print(f"    抓取文章 {j}/2: {url[:50]}...")
                        article = fetcher.fetch_article(url)
                        if article:
                            all_articles.append(article)
                            print(f"    ✓ 成功: {article.get('title', 'Unknown')[:30]}...")
                        else:
                            print(f"    ✗ 失败")
                else:
                    print(f"  ⚠️  未发现文章")
                    
            except Exception as e:
                print(f"  ❌ 抓取失败: {e}")
                continue
        
        print(f"\n📊 抓取完成！共获取 {len(all_articles)} 篇文章")
        
        if all_articles:
            print("\n📋 文章列表:")
            for i, article in enumerate(all_articles, 1):
                title = article.get('title', 'Unknown')
                source = article.get('source', 'Unknown')
                pub_time = article.get('publish_time', 'Unknown')
                print(f"  {i}. {source}《{title[:40]}...》({pub_time})")
            
            # 生成报告
            print(f"\n📝 生成基于扩大时间窗的测试报告...")
            
            report_composer = ReportComposer()
            article_data = {'wechat': all_articles}
            chart_data = {}
            
            report_path = report_composer.compose_report(
                chart_data=chart_data,
                article_data=article_data
            )
            
            if report_path:
                print(f"✅ 报告生成成功: {report_path}")
                
                # 显示报告统计
                try:
                    with open(report_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        word_count = len(content)
                    print(f"📊 报告统计:")
                    print(f"  - 字数: {word_count}")
                    print(f"  - 基于文章数: {len(all_articles)}")
                    print(f"  - 时间窗: 24小时")
                except Exception as e:
                    print(f"⚠️  无法读取报告统计: {e}")
            else:
                print("❌ 报告生成失败")
        else:
            print("⚠️  未获取到任何文章，可能原因：")
            print("1. 搜狗微信反爬虫限制")
            print("2. 公众号名称不匹配")
            print("3. 网络连接问题")
            print("4. 验证码未正确处理")
        
        # 清理浏览器
        try:
            if hasattr(fetcher, '_driver') and fetcher._driver:
                fetcher._driver.quit()
                print("\n🔧 浏览器已关闭")
        except:
            pass
        
        return len(all_articles) > 0
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        return False
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        logger.exception("详细错误信息:")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 测试结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
