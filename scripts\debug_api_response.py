#!/usr/bin/env python3
"""
调试API响应内容
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import requests
import json
from datetime import datetime, timed<PERSON><PERSON>

def debug_api_responses():
    """调试API响应"""
    print("🔍 调试微信文章导出工具API响应")
    print("=" * 60)
    
    base_url = "https://exporter.wxdown.online/dashboard/api"
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': 'https://exporter.wxdown.online/dashboard/account'
    })
    
    # 测试各个端点
    endpoints = [
        ("根路径", ""),
        ("文章接口", "/articles"),
        ("导出接口", "/export"),
        ("账号接口", "/accounts"),
        ("数据接口", "/data")
    ]
    
    for name, endpoint in endpoints:
        print(f"\n📡 测试 {name}: {base_url}{endpoint}")
        
        try:
            response = session.get(f"{base_url}{endpoint}", timeout=15)
            print(f"状态码: {response.status_code}")
            print(f"Content-Type: {response.headers.get('content-type', 'Unknown')}")
            print(f"响应大小: {len(response.content)} 字节")
            
            if response.status_code == 200:
                # 尝试解析JSON
                try:
                    data = response.json()
                    print(f"JSON解析成功:")
                    if isinstance(data, dict):
                        print(f"  类型: 字典，键: {list(data.keys())}")
                        for key, value in data.items():
                            if isinstance(value, list):
                                print(f"    {key}: 列表，长度 {len(value)}")
                            elif isinstance(value, dict):
                                print(f"    {key}: 字典，键 {list(value.keys())}")
                            else:
                                print(f"    {key}: {type(value).__name__} = {str(value)[:50]}")
                    elif isinstance(data, list):
                        print(f"  类型: 列表，长度 {len(data)}")
                        if data and isinstance(data[0], dict):
                            print(f"  列表项结构: {list(data[0].keys())}")
                    else:
                        print(f"  类型: {type(data).__name__}")
                        
                except json.JSONDecodeError:
                    # 不是JSON，显示HTML内容片段
                    content = response.text
                    print(f"非JSON响应，内容片段:")
                    print(f"  前200字符: {content[:200]}")
                    
                    # 查找可能的数据
                    import re
                    
                    # 查找微信链接
                    mp_links = re.findall(r'https://mp\.weixin\.qq\.com/s/[A-Za-z0-9_-]+', content)
                    if mp_links:
                        print(f"  发现微信链接: {len(mp_links)} 个")
                        for i, link in enumerate(mp_links[:3], 1):
                            print(f"    {i}. {link}")
                    
                    # 查找标题
                    titles = re.findall(r'<title>([^<]+)</title>', content, re.IGNORECASE)
                    if titles:
                        print(f"  发现标题: {titles}")
                    
                    # 查找JSON数据
                    json_matches = re.findall(r'\{[^{}]*"[^"]*"[^{}]*\}', content)
                    if json_matches:
                        print(f"  发现JSON片段: {len(json_matches)} 个")
                        for i, match in enumerate(json_matches[:2], 1):
                            print(f"    {i}. {match[:100]}...")
            else:
                print(f"请求失败: {response.status_code}")
                if response.text:
                    print(f"错误信息: {response.text[:200]}")
                    
        except Exception as e:
            print(f"请求异常: {e}")
    
    # 测试POST请求
    print(f"\n📤 测试POST请求...")
    try:
        now = datetime.now()
        start_time = now.replace(hour=15, minute=0, second=0, microsecond=0)
        
        data = {
            'start_date': start_time.strftime('%Y-%m-%d'),
            'end_date': now.strftime('%Y-%m-%d'),
            'format': 'json'
        }
        
        response = session.post(f"{base_url}/export", json=data, timeout=15)
        print(f"POST /export 状态码: {response.status_code}")
        
        if response.status_code == 200:
            try:
                result = response.json()
                print(f"POST响应JSON: {type(result).__name__}")
                if isinstance(result, dict):
                    print(f"  键: {list(result.keys())}")
            except:
                print(f"POST响应非JSON，大小: {len(response.content)}")
                
    except Exception as e:
        print(f"POST请求异常: {e}")

def test_page_content():
    """测试页面内容"""
    print(f"\n📄 测试主页面内容...")
    
    try:
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        response = session.get("https://exporter.wxdown.online/dashboard/account", timeout=15)
        print(f"主页面状态码: {response.status_code}")
        
        if response.status_code == 200:
            content = response.text
            print(f"页面大小: {len(content)} 字符")
            
            # 查找关键信息
            import re
            
            # 查找API配置
            api_configs = re.findall(r'api["\']?\s*[:=]\s*["\']([^"\']+)["\']', content, re.IGNORECASE)
            if api_configs:
                print(f"发现API配置: {api_configs}")
            
            # 查找JavaScript变量
            js_vars = re.findall(r'var\s+(\w+)\s*=\s*["\']([^"\']+)["\']', content)
            if js_vars:
                print(f"发现JS变量: {js_vars[:5]}")
            
            # 查找表单或数据
            forms = re.findall(r'<form[^>]*>(.*?)</form>', content, re.DOTALL)
            print(f"发现表单: {len(forms)} 个")
            
            # 查找可能的数据容器
            data_divs = re.findall(r'<div[^>]*data-[^>]*>', content)
            print(f"发现数据容器: {len(data_divs)} 个")
            
        else:
            print(f"页面访问失败: {response.status_code}")
            
    except Exception as e:
        print(f"页面测试异常: {e}")

def generate_mock_data():
    """生成模拟数据用于测试"""
    print(f"\n🎭 生成模拟数据...")
    
    mock_articles = [
        {
            'title': '股指期权策略分析：当前市场波动率特征',
            'url': 'https://mp.weixin.qq.com/s/mock_article_1',
            'source': '华泰期货研究院',
            'publish_time': '2025-08-12 16:30:00',
            'content': '本文分析当前股指期权市场的波动率特征...',
            'images': []
        },
        {
            'title': '期权交易策略：基于隐含波动率的套利机会',
            'url': 'https://mp.weixin.qq.com/s/mock_article_2',
            'source': '银河期货',
            'publish_time': '2025-08-12 15:45:00',
            'content': '隐含波动率与历史波动率的差异为期权交易提供了套利机会...',
            'images': []
        },
        {
            'title': '宏观经济数据对股指期货的影响分析',
            'url': 'https://mp.weixin.qq.com/s/mock_article_3',
            'source': '国都期权',
            'publish_time': '2025-08-12 17:15:00',
            'content': '最新发布的宏观经济数据显示...',
            'images': []
        }
    ]
    
    print(f"生成 {len(mock_articles)} 篇模拟文章")
    
    # 保存到临时文件
    import json
    with open('temp_mock_articles.json', 'w', encoding='utf-8') as f:
        json.dump(mock_articles, f, ensure_ascii=False, indent=2)
    
    print(f"模拟数据已保存到 temp_mock_articles.json")
    return mock_articles

def main():
    """主函数"""
    debug_api_responses()
    test_page_content()
    mock_data = generate_mock_data()
    
    print(f"\n🎯 调试完成")
    print(f"建议:")
    print(f"1. 如果API返回空数据，可能需要先在网站中配置公众号")
    print(f"2. 可以使用模拟数据测试报告生成功能")
    print(f"3. 考虑使用现有的URL列表方案作为备选")

if __name__ == "__main__":
    main()
