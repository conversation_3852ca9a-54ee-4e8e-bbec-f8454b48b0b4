#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WeChat article fetcher (semi-automatic via Sogou Weixin + Selenium fallback)
- Discovers latest posts for starred accounts from config/wechat_sources.yaml
- Fetches full article HTML (requests first, Selenium render fallback)
- Downloads images (handling data-src & Referer) and rewrites content to Markdown
- Saves structured outputs under output/articles/wechat/<date>/<slug>/

Note: This module focuses on robust plumbing. Selectors are written to be
fault-tolerant; when <PERSON><PERSON><PERSON> shows captcha, user can solve it in the opened
browser and press Enter in the console to continue.
"""

from __future__ import annotations

import os
import re
import time
import json
import hashlib
import datetime as dt
from pathlib import Path
from typing import List, Dict, Optional, Tuple

CACHE_PATH = Path("config/wechat_account_cache.json")

import requests
from bs4 import BeautifulSoup
from loguru import logger
import yaml

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager

from utils.directory_manager import get_output_path

USER_AGENT = (
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
    "(KHTML, like Gecko) Chrome/118.0 Safari/537.36"
)


def _slugify(text: str) -> str:
    text = re.sub(r"[\\\\/:*?\"<>|\s]+", "-", text.strip())
    return text[:80].strip("-") or "article"


def _now_cn() -> dt.datetime:
    # China Standard Time (assume local timezone is CST on user's Windows)
    return dt.datetime.now()


def compute_time_window(reference: Optional[dt.datetime] = None) -> Tuple[dt.datetime, dt.datetime]:
    """Return (start, end) according to the rule:
    - Weekday: today 15:00 to now
    - Weekend: from previous Friday 15:00 to now
    """
    now = reference or _now_cn()
    end = now
    # weekday(): Monday=0 ... Sunday=6
    wd = now.weekday()
    fifteen = now.replace(hour=15, minute=0, second=0, microsecond=0)

    if wd <= 4:  # Mon-Fri
        start = fifteen if now >= fifteen else fifteen - dt.timedelta(days=1)
    else:
        # Saturday or Sunday -> last Friday 15:00
        days_since_friday = (wd - 4)
        start = fifteen - dt.timedelta(days=days_since_friday)
    return start, end


class WeChatFetcher:
    def __init__(self, headless: bool = False):
        self.session = requests.Session()
        self.session.headers.update({"User-Agent": USER_AGENT, "Accept": "text/html"})
        self.config = self._load_config()
        self.headless = headless or not bool(self.config.get("options", {}).get("headless", False))
        self._driver = None
        self._wait = None
        self.time_window = compute_time_window()

    # -------------- setup & config --------------
    def _load_config(self) -> dict:
        cfg_path = Path("config/wechat_sources.yaml")
        if not cfg_path.exists():
            logger.warning("未找到 config/wechat_sources.yaml，将使用空配置。")
            return {"starred_accounts": [], "keywords": [], "options": {}}
        with open(cfg_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f) or {}

    def setup_browser(self) -> bool:
        try:
            logger.info("初始化浏览器以辅助通过验证码与页面渲染…")
            chrome_options = Options()

            # 指定Chrome浏览器路径
            chrome_binary_path = r"D:\待删软件\EasySpider_Windows_x64\EasySpider\resources\app\chrome_win64\chrome.exe"
            if os.path.exists(chrome_binary_path):
                chrome_options.binary_location = chrome_binary_path
                logger.info(f"使用指定的Chrome路径: {chrome_binary_path}")
            else:
                logger.warning(f"指定的Chrome路径不存在: {chrome_binary_path}")

            if self.config.get("options", {}).get("headless"):
                chrome_options.add_argument("--headless=new")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--user-agent=" + USER_AGENT)
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option("useAutomationExtension", False)

            service = Service(ChromeDriverManager().install())
            self._driver = webdriver.Chrome(service=service, options=chrome_options)
            self._driver.execute_script(
                "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
            )
            self._wait = WebDriverWait(self._driver, 30)
            return True
        except Exception as e:
            logger.error(f"浏览器初始化失败: {e}")
            return False

    # -------------- discovery via Sogou --------------
    def _open_sogou_search(self, account_name: str):
        if not self._driver:
            if not self.setup_browser():
                return False
        url = f"https://weixin.sogou.com/weixin?type=1&query={account_name}"
        logger.info(f"打开搜狗微信搜索: {account_name}")
        self._driver.get(url)
        print("\n" + "="*60)
        print("已打开搜狗微信。若出现验证码，请在浏览器中手动完成，然后返回此窗口继续。")
        print("若页面没有显示账号结果，可在浏览器搜索框手动回车，再返回。")
        print("="*60)
        time.sleep(2)
        return True

    def _load_cache(self) -> dict:
        try:
            if CACHE_PATH.exists():
                return json.load(open(CACHE_PATH, "r", encoding="utf-8"))
        except Exception:
            pass
        return {}

    def _save_cache(self, data: dict):
        try:
            json.dump(data, open(CACHE_PATH, "w", encoding="utf-8"), ensure_ascii=False, indent=2)
        except Exception:
            pass

    def discover_article_urls(self, account_name: str, limit: int = 5) -> List[str]:
        """从搜狗账号页发现近期文章链接
        - 容错：账号名可能有OCR误差，这里使用模糊匹配
        - 优先点击官方账号卡片，若失败则在结果页提取所有 mp 链接
        """
        ok = self._open_sogou_search(account_name)
        if not ok:
            return []
        time.sleep(2)
        # 账号卡片：标题可能与输入名称略有差异，尽量点击第一个卡片
        try:
            # 新版搜狗账号卡片常见选择器：.news-box .s-p .tit a 或 #sogou_vr_11002301_box_0 a
            card = None
            for sel in ["#sogou_vr_11002301_box_0 a", ".news-box .s-p .tit a", ".account-box a"]:
                elems = self._driver.find_elements(By.CSS_SELECTOR, sel)
                if elems:
                    card = elems[0]
                    break
            if card:
                card.click()
                self._driver.switch_to.window(self._driver.window_handles[-1])
                time.sleep(2)
        except Exception:
            pass
        # 收集页面内所有 mp 链接（主页或搜索结果页）
        mp_links = set()
        try:
            anchors = self._driver.find_elements(By.CSS_SELECTOR, "a[href]")
            for a in anchors:
                href = a.get_attribute("href") or ""
                if "mp.weixin.qq.com" in href:
                    mp_links.add(href)
        except Exception:
            pass
        urls = list(mp_links)[:limit]
        logger.info(f"发现 {len(urls)} 篇疑似文章链接: {account_name}")
        # 缓存账号与最后一次解析到的URL数，便于后续诊断
        cache = self._load_cache()
        cache[account_name] = {"last_found": len(urls), "ts": _now_cn().strftime("%Y-%m-%d %H:%M:%S")}
        self._save_cache(cache)
        return urls

    # -------------- article fetching --------------
    def _download_image(self, img_url: str, dest_dir: Path, idx: int) -> Optional[str]:
        try:
            headers = {"User-Agent": USER_AGENT, "Referer": "https://mp.weixin.qq.com/"}
            r = self.session.get(img_url, headers=headers, timeout=self.config["options"].get("request_timeout_sec", 20))
            if r.status_code == 200:
                ext = os.path.splitext(img_url.split("?")[0])[1] or ".jpg"
                fname = f"img_{idx:03d}{ext}"
                dest = dest_dir / fname
                with open(dest, "wb") as f:
                    f.write(r.content)
                return fname
        except Exception as e:
            logger.warning(f"图片下载失败: {e}")
        return None

    def _html_to_markdown(self, html: str) -> str:
        # 轻量的 HTML→Markdown（保留段落/标题/列表/链接/图片）
        soup = BeautifulSoup(html, "html.parser")
        md_lines = []
        for tag in soup.children:
            md_lines.append(tag.get_text(" ", strip=True))
        return "\n\n".join([l for l in md_lines if l])

    def _parse_publish_time(self, html: str) -> Optional[str]:
        # 试图从 HTML 中解析发布时间
        # 常见：<meta property="og:release_date" content="2025-08-09"> 或脚本变量 publish_time
        m = re.search(r"og:release_date\" content=\"([0-9\- :]{10,})\"", html)
        if m:
            return m.group(1).strip()
        m2 = re.search(r"publish_time\"?\s*[:=]\s*\"([0-9\- :]{10,})\"", html)
        if m2:
            return m2.group(1).strip()
        return None

    def fetch_article(self, url: str) -> Optional[Dict]:
        try:
            headers = {"User-Agent": USER_AGENT, "Referer": url}
            res = self.session.get(url, headers=headers, timeout=self.config["options"].get("request_timeout_sec", 20))
            html = res.text if res.status_code == 200 else ""
            if (not html or "js_content" not in html) and self.config["options"].get("use_selenium_render_fallback", True):
                # fallback render
                if not self._driver and not self.setup_browser():
                    return None
                self._driver.get(url)
                try:
                    self._wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, "#js_content")))
                    html = self._driver.page_source
                except Exception:
                    html = self._driver.page_source
            if not html:
                return None

            soup = BeautifulSoup(html, "html.parser")
            title = (soup.select_one("#activity-name") or soup.select_one("h1.rich_media_title") or soup.find("title"))
            title_text = (title.get_text(strip=True) if title else "") or "未命名文章"
            account = (soup.select_one("#js_name") or soup.select_one("a#js_name") or soup.find("meta", {"property": "og:article:author"}))
            account_text = account.get_text(strip=True) if account and hasattr(account, 'get_text') else (
                account.get("content", "") if account else ""
            )
            publish_time = self._parse_publish_time(html)

            content_node = soup.select_one("#js_content") or soup.find("div", class_=re.compile("rich_media_content"))
            content_html = str(content_node) if content_node else ""

            # Download images & rewrite
            article_date = (_now_cn().strftime("%Y%m%d"))
            slug = _slugify(title_text)
            base_dir = Path(get_output_path("article", f"wechat/{article_date}/{slug}/placeholder.txt")).parent
            images_dir = base_dir / "images"
            base_dir.mkdir(parents=True, exist_ok=True)
            images_dir.mkdir(parents=True, exist_ok=True)

            content_soup = BeautifulSoup(content_html, "html.parser")
            idx = 1
            for img in content_soup.find_all("img"):
                src = img.get("data-src") or img.get("src")
                if not src:
                    continue
                fname = self._download_image(src, images_dir, idx)
                if fname:
                    img["src"] = f"images/{fname}"
                    if "data-src" in img.attrs:
                        del img["data-src"]
                    idx += 1

            rewritten_html = str(content_soup)
            markdown = self._html_to_markdown(rewritten_html)

            # Keyword filter
            kw = [k for k in (self.config.get("keywords") or []) if k]
            title_lower = title_text.lower()
            body_text = content_soup.get_text(" ", strip=True)
            matched = True if not kw else any((k.lower() in title_lower or k in body_text) for k in kw)
            if self.config.get("options", {}).get("include_only_keyword_matched", True) and not matched:
                logger.info("文章未命中关键词，跳过: {}".format(title_text))
                return None

            # Time window filter (evaluate after we parsed publish_time; 若缺失则放行)
            start, end = self.time_window
            if publish_time:
                try:
                    pt = dt.datetime.fromisoformat(publish_time.replace("/", "-").strip())
                    if not (start <= pt <= end):
                        logger.info(f"文章不在时间窗口内: {title_text} ({publish_time})")
                        return None
                except Exception:
                    pass

            # Save outputs
            meta = {
                "url": url,
                "title": title_text,
                "account": account_text,
                "publish_time": publish_time,
                "saved_at": _now_cn().strftime("%Y-%m-%d %H:%M:%S"),
                "images": sorted([p.name for p in images_dir.glob("*") if p.is_file()]),
            }
            with open(base_dir / "metadata.json", "w", encoding="utf-8") as f:
                json.dump(meta, f, ensure_ascii=False, indent=2)
            with open(base_dir / "article.md", "w", encoding="utf-8") as f:
                f.write(f"# {title_text}\n\n")
                f.write(markdown)

            logger.info(f"✓ 保存文章: {title_text}")
            return {
                "title": title_text,
                "source": account_text or "公众号",
                "publish_time": publish_time or "",
                "content": markdown,
                "file_path": str(base_dir / "article.md"),
                "meta_path": str(base_dir / "metadata.json"),
                "images_dir": str(images_dir),
                "url": url,
            }
        except Exception as e:
            logger.error(f"抓取文章失败: {e}")
            return None

    # -------------- entry point --------------
    def run(self) -> List[Dict]:
        accounts = list(self.config.get("starred_accounts") or [])
        limit = int(self.config.get("options", {}).get("max_articles_per_account", 5))
        results: List[Dict] = []
        if not accounts:
            logger.warning("未配置置顶公众号，跳过抓取。")
            return results

        logger.info("时间窗口: {} → {}".format(*[t.strftime('%Y-%m-%d %H:%M') for t in self.time_window]))
        for name in accounts:
            try:
                urls = self.discover_article_urls(name, limit=limit)
                for url in urls:
                    art = self.fetch_article(url)
                    if art:
                        results.append(art)
            except Exception as e:
                logger.error(f"账号 {name} 处理失败: {e}")
        # 去重（按 url 或 title）
        seen = set()
        deduped = []
        for a in results:
            key = a.get("url") or a.get("title")
            if key in seen:
                continue
            seen.add(key)
            deduped.append(a)
        return deduped

