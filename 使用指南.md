# 📊 中国股指期货报告自动生成系统 - 使用指南

## 🚀 快速开始

### 1. 运行演示版本 (推荐新手)
```bash
python3 run_demo.py
```
- ✅ 使用模拟数据，无需配置
- ✅ 自动生成7张图表
- ✅ 运行完成后自动打开图表查看

### 2. 运行完整版本 (需要Wind API)
```bash
python3 main.py
```
- ✅ 使用真实Wind数据
- ✅ 获取真实舆情文章
- ✅ 生成专业分析报告
- ✅ 运行完成后询问是否打开图表

## 📈 自动打开图表功能

### 运行完成后的选择
程序运行完成后会询问：
```
🌐 是否打开生成的图表？(y/n，默认y):
```

**选择说明**:
- 直接按回车 或 输入 `y`: 自动打开图表
- 输入 `n`: 跳过，稍后手动查看

### 完整版本的图表查看选项
运行 `python3 main.py` 完成后会提供更多选项：
```
请选择查看方式:
1. 打开所有图表 (推荐)
2. 选择特定图表
3. 打开图表文件夹
4. 跳过
```

## 📁 文件位置

### 图表文件
```
output/images/
├── market_sentiment_panel.html     # 市场情绪面板
├── futures_price_trend.html        # 期货价格走势图
├── iv_hv_trend.html                # IV-HV走势图
├── pcr_trend.html                  # PCR走势图
├── volatility_surface.html         # 波动率曲面图
├── capital_flow.html               # 资金流向图
└── margin_trading_scale.html       # 两融规模图
```

### 报告文件
```
output/reports/
└── daily_report_YYYYMMDD_HHMMSS.md  # 每日分析报告
```

### 文章文件
```
output/articles/
├── 财经早餐_*.txt
├── 陆家嘴财经早餐_*.txt
└── 华泰期货研究_*.txt
```

## 🛠️ 其他查看方式

### 1. 使用图表查看器
```bash
python3 view_charts.py
```
- 交互式选择要查看的图表
- 支持一次性打开所有图表

### 2. 手动打开
1. 进入 `output/images/` 文件夹
2. 双击任意 `.html` 文件
3. 在浏览器中查看交互式图表

### 3. 在Finder中查看
1. 按 `Cmd + Space` 打开Spotlight
2. 输入项目文件夹路径
3. 进入 `output/images/` 查看所有图表

## 💡 使用技巧

### 图表交互功能
- **缩放**: 鼠标滚轮放大缩小
- **平移**: 拖拽移动图表
- **悬停**: 鼠标悬停查看具体数据
- **图例**: 点击图例显示/隐藏数据系列

### 最佳实践
1. **首次使用**: 先运行 `python3 run_demo.py` 熟悉系统
2. **日常使用**: 配置Wind API后运行 `python3 main.py`
3. **快速查看**: 使用 `python3 view_charts.py` 重新打开图表
4. **分享报告**: 图表文件可直接分享给他人查看

## 🔧 常见问题

### Q: 图表打不开怎么办？
A: 
1. 确保文件路径正确
2. 尝试直接双击HTML文件
3. 检查浏览器是否正常工作

### Q: 想要PNG格式的图片怎么办？
A: 
1. 在浏览器中打开图表
2. 右键点击 → "复制图像"
3. 粘贴到需要的地方

### Q: 如何重新查看之前生成的图表？
A: 
1. 运行 `python3 view_charts.py`
2. 或直接进入 `output/images/` 文件夹

### Q: 图表显示不完整？
A: 
1. 尝试刷新浏览器页面
2. 检查网络连接
3. 重新生成图表

## 📞 技术支持

如遇问题，请检查：
1. **日志文件**: `logs/` 目录下的日志
2. **输出目录**: `output/` 目录是否正常生成文件
3. **依赖安装**: 运行 `pip install -r requirements.txt`

---

## 🎉 享受您的专业分析报告！

系统已经为您配置好自动打开图表功能，每次运行完成后都会询问是否查看结果。这样您就能立即看到生成的专业图表和分析报告了！

**记住**: 
- 演示版本: `python3 run_demo.py` (新手推荐)
- 完整版本: `python3 main.py` (日常使用)
- 图表查看: `python3 view_charts.py` (重新查看)

祝您使用愉快！📈✨
