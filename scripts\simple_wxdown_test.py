#!/usr/bin/env python3
"""
简化的微信文章导出工具测试
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import time

def test_wxdown_access():
    """测试访问微信文章导出工具"""
    print("🔧 简化测试：访问微信文章导出工具")
    print("=" * 50)
    
    driver = None
    try:
        # 设置Chrome选项
        chrome_options = Options()
        chrome_binary_path = r"D:\待删软件\EasySpider_Windows_x64\EasySpider\resources\app\chrome_win64\chrome.exe"
        
        if os.path.exists(chrome_binary_path):
            chrome_options.binary_location = chrome_binary_path
            print(f"✓ 使用Chrome路径: {chrome_binary_path}")
        
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36")
        
        # 启动浏览器
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("✓ 浏览器启动成功")
        
        # 访问微信文章导出工具
        url = "https://exporter.wxdown.online/dashboard/account"
        print(f"🌐 正在访问: {url}")
        
        driver.get(url)
        print("✓ 页面加载完成")
        
        # 显示页面信息
        print(f"📄 页面标题: {driver.title}")
        print(f"🔗 当前URL: {driver.current_url}")
        
        # 等待用户操作
        print("\n" + "="*50)
        print("📱 浏览器已打开微信文章导出工具")
        print("请在浏览器中执行以下操作：")
        print("1. 注册/登录账号（如需要）")
        print("2. 查看界面功能")
        print("3. 尝试添加公众号")
        print("4. 观察是否能正常使用")
        print("="*50)
        
        # 等待用户确认
        input("完成测试后，按回车键关闭浏览器...")
        
        print("✅ 测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        if driver:
            try:
                driver.quit()
                print("🔧 浏览器已关闭")
            except:
                pass

def show_manual_solution():
    """显示手动解决方案"""
    print("\n" + "="*50)
    print("💡 手动解决方案")
    print("="*50)
    print("如果微信文章导出工具可以正常使用，请按以下步骤操作：")
    print()
    print("1. 在网站中配置要监控的公众号：")
    for account in ["华泰期货研究院", "银河期货", "国都期权", "东方财富网", "混沌天成研究"]:
        print(f"   - {account}")
    print()
    print("2. 导出最新文章的URL")
    print("3. 将URL复制到 input/article_urls.txt 文件中")
    print("4. 运行命令: python scripts/validate_plan_b.py")
    print()
    print("这样就能获得基于最新文章的完整报告！")
    print("="*50)

def main():
    """主函数"""
    print("🔍 微信文章导出工具简化测试")
    print(f"网站: https://exporter.wxdown.online/dashboard/account")
    print()
    
    # 测试访问
    success = test_wxdown_access()
    
    if success:
        print("\n🎉 浏览器访问测试成功！")
        show_manual_solution()
    else:
        print("\n❌ 浏览器访问测试失败")
        print("请检查Chrome浏览器配置或网络连接")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
