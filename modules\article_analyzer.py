#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ArticleAnalyzer: 对单篇公众号文章进行“强制消化”，输出结构化结果，尽量对齐 V15.2 要求。
- 若提供 openai_client，则使用严格 JSON 提示词让模型抽取；
- 否则使用启发式方法从正文中提取要点，产出近似结构。
"""
from __future__ import annotations

import re
from dataclasses import dataclass, asdict
from typing import List, Dict, Optional
from loguru import logger


@dataclass
class ArticleSummary:
    title: str
    url: str
    source: str
    publish_time: str
    key_points: List[str]
    key_data: List[Dict]
    logic_chain: str
    index_impact: Dict[str, Dict]
    weights: List[Dict]
    risks: List[str]
    images: List[str]
    image_findings: List[str]


class ArticleAnalyzer:
    def __init__(self, openai_client=None):
        self.openai_client = openai_client

    # ---------------- Heuristic fallback -----------------
    def _heuristic_extract(self, article: Dict) -> ArticleSummary:
        text = article.get("content", "")
        title = article.get("title", "")
        url = article.get("url", "")
        source = article.get("source", "公众号")
        publish_time = article.get("publish_time", "")
        images = []
        try:
            # 尝试从 metadata.json 路径收集图片
            imgdir = article.get("images_dir")
            import os
            if imgdir and os.path.isdir(imgdir):
                images = sorted([f for f in os.listdir(imgdir) if os.path.isfile(os.path.join(imgdir, f))])
        except Exception:
            pass

        # 简单要点：抓取包含关键字的句子
        key_points = []
        for kw in ["宏观", "股指", "期权", "波动率", "IV", "HV", "PCR", "北向", "基差", "成交"]:
            m = re.search(rf"(.{{0,30}}{kw}.{{0,60}})", text)
            if m:
                s = m.group(1)
                if s not in key_points:
                    key_points.append(s)
        if not key_points and title:
            key_points = [title]

        # 抓数字数据：如百分比/亿元/点位
        key_data = []
        for m in re.finditer(r"([\-\+]?[0-9]+(?:\.[0-9]+)?)(%|亿元|亿|点|bp)", text):
            val, unit = m.group(1), m.group(2)
            key_data.append({"value": val, "unit": unit, "snippet": text[max(0, m.start()-20): m.end()+20]})
            if len(key_data) >= 10:
                break

        # 逻辑链（启发）：
        logic_chain = "宏观/政策→风险偏好/流动性→指数与波动率→期权策略/基差结构"

        # 对股指影响（启发式全填“中性偏谨慎”）
        index_impact = {
            "SH50": {"direction": "中性/偏弱", "reason": "权重蓝筹受宏观/资金面影响"},
            "HS300": {"direction": "中性/偏弱", "reason": "广谱指数受风险偏好走弱影响"},
            "CSI1000": {"direction": "波动较大", "reason": "小盘更敏感，受情绪驱动"},
        }

        # 权重评分（1-5）
        weights = []
        def add_weight(kind, score, reason):
            weights.append({"类型": kind, "评分": score, "理由": reason})
        if re.search("降息|通胀|PMI|非农|美联储|政策", text):
            add_weight("宏观/政策", 4, "涉及宏观数据或政策指引")
        if re.search("北向|两融|资金|成交", text):
            add_weight("资金面", 3, "涉及北向/两融/成交")
        if re.search("期权|IV|HV|波动率|PCR", text):
            add_weight("衍生品", 3, "涉及波动率与期权指标")
        if not weights:
            add_weight("背景", 2, "与A股关联一般，但有参考价值")

        # 风险
        risks = ["政策超预期变化", "外围大幅波动", "事件驱动导致波动率飙升"]

        # 图片检查
        image_findings = []
        if images:
            image_findings.append("已保存图片{}张；未进行OCR，如需IV-HV表格请人工确认".format(len(images)))
        else:
            image_findings.append("未发现图片或图片未能下载")

        return ArticleSummary(
            title=title, url=url, source=source, publish_time=publish_time,
            key_points=key_points, key_data=key_data, logic_chain=logic_chain,
            index_impact=index_impact, weights=weights, risks=risks,
            images=images, image_findings=image_findings,
        )

    # ---------------- OpenAI JSON extraction -----------------
    def _ai_extract(self, article: Dict) -> Optional[ArticleSummary]:
        try:
            # 仅当ReportComposer初始化了Gemini客户端时才调用
            if not getattr(self.openai_client, 'gemini_model', None):
                return None
            import json
            text = article.get("content", "")
            meta = {k: article.get(k, "") for k in ["title", "url", "source", "publish_time"]}
            images = []
            if article.get("images_dir"):
                import os
                try:
                    images = sorted(os.listdir(article["images_dir"]))
                except Exception:
                    pass
            prompt = (
                "你是一名期权策略师，请严格输出JSON（不要任何额外文本）。字段: "
                "title,url,source,publish_time,key_points(数组),"
                "key_data(数组:指标/数值/时间/片段),logic_chain,"
                "index_impact(对象:SH50/HS300/CSI1000:direction/reason),"
                "weights(数组:类型/评分/理由),risks(数组),image_findings(数组)。"
                "若图片未识别到IV-HV/PCR/基差等字段，请在image_findings中写‘未找到或无法识别’。\n"
                f"文章元数据:{meta}\n图片数量:{len(images)}; 图片文件:{images}\n\n正文:\n{text}"
            )
            # 通过ReportComposer的gemini_generate调用
            content = self.openai_client.gemini_generate(prompt)
            if not content:
                return None
            data = json.loads(content)
            return ArticleSummary(
                title=data.get("title", meta.get("title", "")),
                url=data.get("url", meta.get("url", "")),
                source=data.get("source", meta.get("source", "")),
                publish_time=data.get("publish_time", meta.get("publish_time", "")),
                key_points=data.get("key_points", []),
                key_data=data.get("key_data", []),
                logic_chain=data.get("logic_chain", ""),
                index_impact=data.get("index_impact", {}),
                weights=data.get("weights", []),
                risks=data.get("risks", []),
                images=images,
                image_findings=data.get("image_findings", []),
            )
        except Exception as e:
            logger.error(f"AI抽取失败，将使用启发式: {e}")
            return None

    # ---------------- public API -----------------
    def analyze(self, article: Dict) -> Dict:
        # 默认走启发式，降低对网络/模型的依赖；后续仅在报告生成阶段调用Gemini
        res = self._heuristic_extract(article)
        return asdict(res)

