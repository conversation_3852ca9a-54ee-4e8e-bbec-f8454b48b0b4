#!/usr/bin/env python3
"""
检查API客户端是否抓取到真实文章
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.wxdown_api_client import WxDownAPIClient
from datetime import datetime
import json

def check_api_articles():
    """检查API抓取的文章"""
    print("🔍 检查微信文章导出工具API抓取结果")
    print("=" * 60)
    
    try:
        # 初始化API客户端
        client = WxDownAPIClient()
        print(f"✓ API客户端初始化成功")
        print(f"  时间窗: {client.time_window[0]} -> {client.time_window[1]}")
        print(f"  关键词数量: {len(client.keywords)}")
        
        # 尝试抓取文章
        print(f"\n🔄 开始抓取文章...")
        articles = client.get_articles_auto()
        
        print(f"\n📊 抓取结果:")
        print(f"  文章数量: {len(articles)}")
        
        if articles:
            print(f"\n📋 文章详情:")
            for i, article in enumerate(articles[:5], 1):
                title = article.get('title', 'Unknown')
                url = article.get('url', 'Unknown')
                source = article.get('source', 'Unknown')
                pub_time = article.get('publish_time', 'Unknown')
                
                print(f"  {i}. 标题: {title[:50]}...")
                print(f"     URL: {url[:60]}...")
                print(f"     来源: {source}")
                print(f"     时间: {pub_time}")
                print()
            
            # 检查是否为真实数据
            real_urls = [a for a in articles if 'mp.weixin.qq.com' in a.get('url', '')]
            mock_urls = [a for a in articles if 'mock_' in a.get('url', '')]
            
            print(f"📈 数据分析:")
            print(f"  真实微信URL: {len(real_urls)} 个")
            print(f"  模拟URL: {len(mock_urls)} 个")
            
            if real_urls:
                print(f"✅ 发现真实微信文章数据")
                return True, articles
            else:
                print(f"⚠️  未发现真实微信文章，可能使用了模拟数据")
                return False, articles
        else:
            print(f"❌ 未抓取到任何文章")
            return False, []
            
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False, []

def check_recent_reports():
    """检查最近生成的报告"""
    print(f"\n📄 检查最近生成的报告...")
    
    import glob
    report_pattern = "output/reports/daily_report_*.md"
    reports = sorted(glob.glob(report_pattern), reverse=True)
    
    if reports:
        latest_report = reports[0]
        print(f"✓ 最新报告: {latest_report}")
        
        # 检查报告内容
        with open(latest_report, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"  文件大小: {len(content)} 字符")
        
        # 检查日期
        if "2025年8月4日" in content:
            print(f"⚠️  发现错误日期: 2025年8月4日")
        elif "2025年8月13日" in content:
            print(f"✅ 日期正确: 2025年8月13日")
        else:
            print(f"❓ 未找到明确日期")
        
        # 检查是否基于模拟数据
        if "模拟数据" in content or "mock_" in content:
            print(f"⚠️  报告可能基于模拟数据")
        else:
            print(f"✅ 报告基于真实数据")
        
        return latest_report, content
    else:
        print(f"❌ 未找到报告文件")
        return None, None

def main():
    """主函数"""
    print("🔍 API抓取文章验证检查")
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    # 1. 检查API抓取结果
    has_real_data, articles = check_api_articles()
    
    # 2. 检查最近的报告
    report_path, report_content = check_recent_reports()
    
    # 3. 总结
    print(f"\n🎯 检查结果总结")
    print("=" * 60)
    
    if has_real_data:
        print("✅ API成功抓取到真实微信文章数据")
        print(f"  - 文章数量: {len(articles)}")
        print(f"  - 数据来源: 微信文章导出工具API")
    else:
        print("❌ API未抓取到真实数据")
        print("  可能原因:")
        print("  1. API接口需要认证或特殊配置")
        print("  2. 网站返回HTML而非JSON数据")
        print("  3. 时间窗内没有符合条件的文章")
        print("  4. 关键词过滤过于严格")
    
    if report_path:
        print(f"\n📊 最新报告状态:")
        print(f"  - 文件路径: {report_path}")
        if "2025年8月4日" in report_content:
            print(f"  - 日期状态: ❌ 错误（显示8月4日）")
        else:
            print(f"  - 日期状态: ✅ 正确")
        
        if has_real_data:
            print(f"  - 数据来源: ✅ 真实API数据")
        else:
            print(f"  - 数据来源: ⚠️  模拟数据")
    
    print(f"\n💡 建议:")
    if not has_real_data:
        print("1. 使用手动URL收集方案获取真实数据")
        print("2. 将真实文章URL添加到 input/article_urls.txt")
        print("3. 运行 python scripts/validate_plan_b.py")
    
    print("4. 重新生成报告以验证日期修复")

if __name__ == "__main__":
    main()
