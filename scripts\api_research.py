#!/usr/bin/env python3
"""
研究微信文章导出工具的API接口
"""

import requests
import json
import time
from datetime import datetime, timedelta

def test_wxdown_api():
    """测试微信文章导出工具API"""
    print("🔍 测试微信文章导出工具API")
    print("=" * 50)
    
    base_url = "https://exporter.wxdown.online"
    api_base = f"{base_url}/dashboard/api"
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Referer': f'{base_url}/dashboard/account'
    })
    
    # 测试基础端点
    endpoints = [
        "",
        "/accounts",
        "/articles", 
        "/export",
        "/data",
        "/user",
        "/auth"
    ]
    
    results = {}
    
    for endpoint in endpoints:
        url = f"{api_base}{endpoint}"
        try:
            print(f"测试: {url}")
            response = session.get(url, timeout=10)
            
            results[endpoint] = {
                'status': response.status_code,
                'content_type': response.headers.get('content-type', ''),
                'size': len(response.content)
            }
            
            if response.status_code == 200:
                print(f"✅ {endpoint} - 成功 ({response.status_code})")
                try:
                    data = response.json()
                    if isinstance(data, dict):
                        print(f"   JSON响应，键: {list(data.keys())}")
                    elif isinstance(data, list):
                        print(f"   JSON数组，长度: {len(data)}")
                except:
                    print(f"   非JSON响应")
            elif response.status_code == 401:
                print(f"🔐 {endpoint} - 需要认证 (401)")
            elif response.status_code == 403:
                print(f"🚫 {endpoint} - 访问被拒绝 (403)")
            elif response.status_code == 404:
                print(f"❌ {endpoint} - 未找到 (404)")
            else:
                print(f"⚠️  {endpoint} - 状态码: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ {endpoint} - 请求超时")
            results[endpoint] = {'status': 'timeout'}
        except requests.exceptions.ConnectionError:
            print(f"🔌 {endpoint} - 连接错误")
            results[endpoint] = {'status': 'connection_error'}
        except Exception as e:
            print(f"❌ {endpoint} - 错误: {e}")
            results[endpoint] = {'status': 'error', 'error': str(e)}
    
    return results

def analyze_page_content():
    """分析页面内容获取API信息"""
    print(f"\n📄 分析页面内容...")
    
    try:
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        response = session.get("https://exporter.wxdown.online/dashboard/account", timeout=15)
        
        if response.status_code == 200:
            content = response.text
            print(f"✅ 页面获取成功，内容长度: {len(content)}")
            
            # 查找API相关信息
            import re
            
            # 查找可能的API端点
            api_patterns = [
                r'["\'](/api/[^"\']+)["\']',
                r'["\'](/dashboard/api/[^"\']+)["\']',
                r'fetch\(["\']([^"\']*api[^"\']*)["\']',
                r'axios\.[a-z]+\(["\']([^"\']*api[^"\']*)["\']'
            ]
            
            found_apis = set()
            for pattern in api_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                found_apis.update(matches)
            
            if found_apis:
                print(f"发现 {len(found_apis)} 个可能的API端点:")
                for api in sorted(found_apis):
                    print(f"   {api}")
            else:
                print("未在页面中发现明显的API端点")
                
            # 查找配置信息
            config_patterns = [
                r'apiUrl["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'baseURL["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                r'API_BASE["\']?\s*[:=]\s*["\']([^"\']+)["\']'
            ]
            
            for pattern in config_patterns:
                matches = re.findall(pattern, content, re.IGNORECASE)
                if matches:
                    print(f"发现API配置: {matches}")
                    
        else:
            print(f"❌ 页面获取失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 页面分析失败: {e}")

def generate_api_client_template():
    """生成API客户端模板"""
    print(f"\n💻 生成API客户端模板...")
    
    template = '''
class WxDownAPIClient:
    """微信文章导出工具API客户端"""
    
    def __init__(self, token=None):
        self.base_url = "https://exporter.wxdown.online/dashboard/api"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json'
        })
        
        if token:
            self.session.headers['Authorization'] = f'Bearer {token}'
    
    def get_accounts(self):
        """获取账号列表"""
        response = self.session.get(f"{self.base_url}/accounts")
        return response.json() if response.status_code == 200 else None
    
    def get_articles(self, account_id=None, start_date=None, end_date=None):
        """获取文章列表"""
        params = {}
        if account_id:
            params['account_id'] = account_id
        if start_date:
            params['start_date'] = start_date
        if end_date:
            params['end_date'] = end_date
            
        response = self.session.get(f"{self.base_url}/articles", params=params)
        return response.json() if response.status_code == 200 else None
    
    def export_articles(self, filters=None):
        """导出文章"""
        data = filters or {}
        response = self.session.post(f"{self.base_url}/export", json=data)
        return response.json() if response.status_code == 200 else None
'''
    
    print("建议的API客户端模板:")
    print(template)

def main():
    """主函数"""
    print("🔍 微信文章导出工具API研究")
    
    # 1. 测试API端点
    results = test_wxdown_api()
    
    # 2. 分析页面内容
    analyze_page_content()
    
    # 3. 生成客户端模板
    generate_api_client_template()
    
    print(f"\n🎯 研究完成！")
    
    # 总结结果
    working_endpoints = [ep for ep, result in results.items() 
                        if isinstance(result, dict) and result.get('status') == 200]
    
    if working_endpoints:
        print(f"✅ 发现 {len(working_endpoints)} 个可用的API端点")
        print("建议基于API实现自动化抓取")
    else:
        print("⚠️  未发现可用的API端点")
        print("建议使用页面自动化方案")

if __name__ == "__main__":
    main()
