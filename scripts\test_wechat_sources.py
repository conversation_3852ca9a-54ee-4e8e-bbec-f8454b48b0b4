#!/usr/bin/env python3
"""
测试置顶公众号配置和时间窗过滤
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.article_fetcher import ArticleFetcher
from modules.wechat_fetcher import WeChatFetcher
import yaml
from datetime import datetime, timedelta

def test_wechat_sources_config():
    """测试微信公众号配置"""
    print("=" * 50)
    print("测试置顶公众号配置")
    print("=" * 50)
    
    # 读取配置
    try:
        with open('config/wechat_sources.yaml', 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        print(f"✓ 配置文件读取成功")
        print(f"  - 配置的公众号数量: {len(config.get('starred_accounts', []))}")
        print(f"  - 关键词数量: {len(config.get('keywords', []))}")

        # 显示配置详情
        accounts = config.get('starred_accounts', [])
        for i, account in enumerate(accounts[:5], 1):  # 只显示前5个
            print(f"  - 账号{i}: {account}")
        
        keywords = config.get('keywords', [])
        print(f"  - 关键词: {', '.join(keywords[:10])}")  # 只显示前10个
        
    except Exception as e:
        print(f"✗ 配置文件读取失败: {e}")
        return False
    
    return True

def test_time_window():
    """测试时间窗过滤逻辑"""
    print("\n" + "=" * 50)
    print("测试时间窗过滤逻辑")
    print("=" * 50)
    
    try:
        fetcher = WeChatFetcher(headless=True)

        # 获取当前时间窗
        start_time, end_time = fetcher.time_window
        
        print(f"✓ 时间窗计算成功")
        print(f"  - 开始时间: {start_time}")
        print(f"  - 结束时间: {end_time}")
        print(f"  - 时间范围: {(end_time - start_time).total_seconds() / 3600:.1f} 小时")
        
        # 测试今日是否为工作日
        today = datetime.now()
        is_weekday = today.weekday() < 5
        print(f"  - 今日是工作日: {is_weekday}")
        
        return True
        
    except Exception as e:
        print(f"✗ 时间窗测试失败: {e}")
        return False

def test_article_fetcher_init():
    """测试文章获取器初始化"""
    print("\n" + "=" * 50)
    print("测试文章获取器初始化")
    print("=" * 50)
    
    try:
        fetcher = ArticleFetcher()
        print("✓ ArticleFetcher 初始化成功")
        
        # 测试配置加载
        if hasattr(fetcher, 'wechat_fetcher'):
            print("✓ WeChatFetcher 子模块加载成功")
        else:
            print("✗ WeChatFetcher 子模块未加载")
            
        return True
        
    except Exception as e:
        print(f"✗ ArticleFetcher 初始化失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔍 置顶公众号功能测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    results = []
    
    # 测试1: 配置文件
    results.append(test_wechat_sources_config())
    
    # 测试2: 时间窗逻辑
    results.append(test_time_window())
    
    # 测试3: 文章获取器初始化
    results.append(test_article_fetcher_init())
    
    # 总结
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过测试: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有测试通过！置顶公众号功能配置正常")
        print("\n💡 建议:")
        print("1. 确保Chrome浏览器已安装并可访问")
        print("2. 运行 python main.py 并选择启用置顶账号抓取")
        print("3. 手动处理验证码以完成半自动抓取")
    else:
        print("❌ 部分测试失败，请检查配置")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
