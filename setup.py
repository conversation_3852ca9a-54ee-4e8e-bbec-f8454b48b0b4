#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安装和设置脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def install_dependencies():
    """安装依赖包"""
    print("📦 正在安装依赖包...")
    
    try:
        # 升级pip
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        
        # 安装依赖
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        
        print("✅ 依赖包安装完成")
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        return False

def setup_config():
    """设置配置文件"""
    print("⚙️  设置配置文件...")
    
    config_template = Path("config/config_template.py")
    config_file = Path("config/config.py")
    
    if not config_file.exists() and config_template.exists():
        # 复制模板文件
        import shutil
        shutil.copy(config_template, config_file)
        print(f"✅ 配置文件已创建: {config_file}")
        
        print("\n⚠️  请编辑 config/config.py 文件，填入您的API密钥:")
        print("   - OpenAI API密钥")
        print("   - 确保Wind终端已安装并可用")
        
    else:
        print("✅ 配置文件已存在")
    
    return True

def create_directories():
    """创建必要目录"""
    print("📁 创建输出目录...")
    
    directories = [
        "output",
        "output/images",
        "output/articles", 
        "output/reports",
        "output/temp",
        "logs"
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
    
    print("✅ 目录创建完成")
    return True

def run_tests():
    """运行基础测试"""
    print("🧪 运行基础测试...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pytest", "tests/test_basic.py", "-v"])
        print("✅ 基础测试通过")
        return True
        
    except subprocess.CalledProcessError:
        print("⚠️  部分测试失败，但系统仍可运行")
        return True
    except FileNotFoundError:
        print("⚠️  pytest未安装，跳过测试")
        return True

def main():
    """主安装流程"""
    print("🚀 中国股指期货报告自动生成系统 - 安装程序")
    print("=" * 60)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        return False
    
    print(f"✅ Python版本: {sys.version}")
    
    # 安装依赖
    if not install_dependencies():
        return False
    
    # 设置配置
    if not setup_config():
        return False
    
    # 创建目录
    if not create_directories():
        return False
    
    # 运行测试
    run_tests()
    
    print("\n" + "=" * 60)
    print("🎉 安装完成！")
    print("\n📋 下一步:")
    print("1. 编辑 config/config.py 文件，填入API密钥")
    print("2. 确保Wind终端已启动（如果使用真实数据）")
    print("3. 运行演示: python run_demo.py")
    print("4. 运行完整版本: python main.py")
    print("\n💡 如有问题，请查看README.md文档")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
