import datetime as dt
from modules.wechat_fetcher import compute_time_window


def test_weekday_after_15():
    ref = dt.datetime(2025, 8, 11, 16, 30)  # Monday
    start, end = compute_time_window(ref)
    assert start == dt.datetime(2025, 8, 11, 15, 0)
    assert end == ref


def test_weekend_to_friday():
    ref = dt.datetime(2025, 8, 10, 10, 0)  # Sunday
    start, _ = compute_time_window(ref)
    assert start == dt.datetime(2025, 8, 8, 15, 0)  # Friday 15:00

