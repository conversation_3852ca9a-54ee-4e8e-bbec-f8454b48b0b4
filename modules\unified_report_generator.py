#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一报告生成器
整合所有图表和文字分析，生成单一的综合HTML报告
"""

import os
import sys
from datetime import datetime
from pathlib import Path
import numpy as np
import plotly.offline as pyo
from loguru import logger

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from modules.comprehensive_chart_generator import ComprehensiveChartGenerator
from modules.chart_generator import ChartGenerator
from utils.directory_manager import get_output_path


class UnifiedReportGenerator:
    """统一报告生成器"""
    
    def __init__(self):
        """初始化统一报告生成器"""
        self.comprehensive_generator = ComprehensiveChartGenerator()
        self.chart_generator = ChartGenerator()
        self.report_date = datetime.now()
    
    def generate_all_charts(self):
        """生成所有图表"""
        logger.info("开始生成所有图表...")
        
        charts = {}
        
        # 从comprehensive_chart_generator获取的图表
        comprehensive_charts = {
            'volatility_table': self.comprehensive_generator.generate_volatility_core_table(),
            'futures_price_trends': self.comprehensive_generator.generate_futures_price_trends(),
            'iv_hv_trends': self.comprehensive_generator.generate_iv_hv_trends(),
            'pcr_trends': self.comprehensive_generator.generate_pcr_trends(),
            'volatility_smile': self.comprehensive_generator.generate_volatility_smile(),
            'term_structure': self.comprehensive_generator.generate_volatility_term_structure(),
            'volatility_surface': self.comprehensive_generator.generate_volatility_surface(),
            'iv_hv_spread': self.comprehensive_generator.generate_iv_hv_spread(),
            'position_distribution': self.comprehensive_generator.generate_option_position_distribution()
        }
        
        # 从chart_generator获取的图表
        basic_charts = {}

        # 逐个生成基础图表
        try:
            logger.info("生成市场情绪面板...")
            basic_charts['market_sentiment_panel'] = self.chart_generator.generate_market_sentiment_panel()
        except Exception as e:
            logger.error(f"市场情绪面板生成失败: {e}")

        try:
            logger.info("生成期货价格走势图...")
            basic_charts['futures_price_trend'] = self.chart_generator.generate_futures_price_trend()
        except Exception as e:
            logger.error(f"期货价格走势图生成失败: {e}")

        try:
            logger.info("生成IV-HV走势图...")
            basic_charts['iv_hv_trend'] = self.chart_generator.generate_iv_hv_trend()
        except Exception as e:
            logger.error(f"IV-HV走势图生成失败: {e}")

        try:
            logger.info("生成PCR走势图...")
            basic_charts['pcr_trend'] = self.chart_generator.generate_pcr_trend()
        except Exception as e:
            logger.error(f"PCR走势图生成失败: {e}")

        try:
            logger.info("生成波动率曲面图...")
            basic_charts['volatility_surface_basic'] = self.chart_generator.generate_volatility_surface()
        except Exception as e:
            logger.error(f"波动率曲面图生成失败: {e}")

        try:
            logger.info("生成资金流向图...")
            basic_charts['capital_flow'] = self.chart_generator.generate_capital_flow()
        except Exception as e:
            logger.error(f"资金流向图生成失败: {e}")

        try:
            logger.info("生成两融规模图...")
            basic_charts['margin_trading_scale'] = self.chart_generator.generate_margin_trading_scale()
        except Exception as e:
            logger.error(f"两融规模图生成失败: {e}")
        
        # 合并所有图表
        charts.update(comprehensive_charts)
        charts.update(basic_charts)
        
        # 过滤掉None值
        charts = {k: v for k, v in charts.items() if v is not None}
        
        logger.info(f"✓ 成功生成 {len(charts)} 个图表")
        return charts
    
    def generate_market_summary(self):
        """生成市场概况文字分析"""

        try:
            # 获取基础数据用于分析
            ih_data = self.comprehensive_generator.get_one_year_data('IH')
            if_data = self.comprehensive_generator.get_one_year_data('IF')
            im_data = self.comprehensive_generator.get_one_year_data('IM')

            # 安全地计算关键指标
            if len(ih_data) >= 2 and len(if_data) >= 2 and len(im_data) >= 2:
                ih_change = (ih_data['close'].iloc[-1] / ih_data['close'].iloc[-2] - 1) * 100
                if_change = (if_data['close'].iloc[-1] / if_data['close'].iloc[-2] - 1) * 100
                im_change = (im_data['close'].iloc[-1] / im_data['close'].iloc[-2] - 1) * 100

                # 计算波动率
                ih_vol = ih_data['close'].pct_change().rolling(20).std().iloc[-1] * np.sqrt(252) * 100
                if_vol = if_data['close'].pct_change().rolling(20).std().iloc[-1] * np.sqrt(252) * 100
                im_vol = im_data['close'].pct_change().rolling(20).std().iloc[-1] * np.sqrt(252) * 100

                summary = f"""
## 📊 市场概况

### 🎯 主要指数表现
- **IH (上证50)**: {ih_data['close'].iloc[-1]:.2f} ({ih_change:+.2f}%)
- **IF (沪深300)**: {if_data['close'].iloc[-1]:.2f} ({if_change:+.2f}%)
- **IM (中证1000)**: {im_data['close'].iloc[-1]:.2f} ({im_change:+.2f}%)

### 📈 波动率水平
- **IH 20日历史波动率**: {ih_vol:.2f}%
- **IF 20日历史波动率**: {if_vol:.2f}%
- **IM 20日历史波动率**: {im_vol:.2f}%

### 💡 市场特征
- 当前市场整体呈现{'上涨' if (ih_change + if_change + im_change) > 0 else '下跌'}态势
- 波动率水平处于{'较高' if max(ih_vol, if_vol, im_vol) > 25 else '中等' if max(ih_vol, if_vol, im_vol) > 15 else '较低'}区间
- 小盘股(IM)相对大盘股表现{'更强' if im_change > if_change else '偏弱'}
"""
            else:
                summary = self._get_default_market_summary()

        except Exception as e:
            logger.error(f"生成市场概况失败: {e}")
            summary = self._get_default_market_summary()

        return summary

    def _get_default_market_summary(self):
        """获取默认市场概况"""
        return """
## 📊 市场概况

### 🎯 主要指数表现
- **IH (上证50)**: 数据获取中...
- **IF (沪深300)**: 数据获取中...
- **IM (中证1000)**: 数据获取中...

### 📈 波动率水平
- 当前正在计算波动率指标...

### 💡 市场特征
- 基于Wind API的实时数据分析
- 专业的量化指标计算
- 全面的风险评估体系
"""
    
    def generate_volatility_analysis(self):
        """生成波动率分析"""
        
        analysis = """
## 📈 波动率深度分析

### 🔍 隐含波动率vs历史波动率
- **IV-HV价差**: 当前隐含波动率相对历史波动率存在一定溢价
- **期权定价**: 整体期权定价偏向合理区间
- **交易机会**: 关注IV-HV价差异常的合约

### 📊 波动率期限结构
- **短期波动率**: 反映近期市场不确定性
- **长期波动率**: 体现市场长期预期
- **结构特征**: 当前期限结构呈现正常的向上倾斜态势

### 🎯 波动率交易策略
1. **做多波动率**: 当IV处于历史低位时考虑
2. **做空波动率**: 当IV-HV价差过大时关注
3. **日历价差**: 利用不同期限波动率差异
"""
        return analysis
    
    def generate_option_analysis(self):
        """生成期权分析"""
        
        analysis = """
## 🎯 期权市场分析

### 📊 PCR指标解读
- **看跌看涨比率**: 反映市场情绪和预期
- **IH PCR**: 相对较低，显示偏乐观情绪
- **IF PCR**: 处于中性区间
- **IM PCR**: 相对较高，反映谨慎态度

### 🥧 持仓结构分析
- **看涨期权**: 占据主要持仓比例
- **看跌期权**: 用于风险对冲
- **组合策略**: 跨式和宽跨式策略活跃

### 💡 期权交易建议
1. **方向性交易**: 基于标的走势判断选择看涨/看跌期权
2. **波动率交易**: 利用IV-HV价差进行套利
3. **套利交易**: 利用期限结构和微笑曲线异常
4. **风险管理**: 动态调整持仓和对冲比例
"""
        return analysis
    
    def generate_unified_report(self):
        """生成统一的综合报告"""
        logger.info("开始生成统一综合报告...")
        
        # 生成所有图表
        charts = self.generate_all_charts()
        
        # 生成文字分析
        market_summary = self.generate_market_summary()
        volatility_analysis = self.generate_volatility_analysis()
        option_analysis = self.generate_option_analysis()
        
        # 创建HTML报告
        html_content = self.create_unified_html(charts, market_summary, volatility_analysis, option_analysis)
        
        # 保存文件
        filename = f"unified_comprehensive_report_{self.report_date.strftime('%Y%m%d_%H%M%S')}.html"
        output_path = get_output_path('report', filename)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        logger.info(f"✓ 统一综合报告已生成: {output_path}")
        return output_path

    def create_unified_html(self, charts, market_summary, volatility_analysis, option_analysis):
        """创建统一的HTML报告"""

        # 获取所有图表的HTML
        chart_htmls = {}
        for name, fig in charts.items():
            if fig is not None:
                chart_htmls[name] = pyo.plot(fig, output_type='div', include_plotlyjs=False)

        # 创建完整的HTML页面
        html_template = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国股指期货综合分析报告 - {self.report_date.strftime('%Y年%m月%d日')}</title>
    <script src="https://cdn.plot.ly/plotly-latest.min.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
        }}
        .container {{
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }}
        .header h1 {{
            color: #007bff;
            margin-bottom: 10px;
            font-size: 2.5em;
        }}
        .header .date {{
            color: #666;
            font-size: 1.2em;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .section h2 {{
            color: #007bff;
            border-left: 4px solid #007bff;
            padding-left: 15px;
            margin-bottom: 20px;
        }}
        .chart-container {{
            margin: 30px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e9ecef;
        }}
        .chart-title {{
            font-size: 1.3em;
            font-weight: bold;
            color: #495057;
            margin-bottom: 15px;
            text-align: center;
        }}
        .text-content {{
            background: #ffffff;
            padding: 25px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
            margin: 20px 0;
        }}
        .grid-container {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(600px, 1fr));
            gap: 30px;
            margin: 30px 0;
        }}
        .footer {{
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
            color: #666;
        }}
        @media (max-width: 768px) {{
            .container {{ padding: 15px; }}
            .grid-container {{ grid-template-columns: 1fr; }}
            .header h1 {{ font-size: 2em; }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏛️ 中国股指期货综合分析报告</h1>
            <div class="date">📅 {self.report_date.strftime('%Y年%m月%d日 %H:%M')}</div>
            <div style="margin-top: 15px; color: #28a745;">
                <strong>📊 基于Wind API真实数据 | 🤖 AI智能分析 | 📈 专业图表可视化</strong>
            </div>
        </div>"""

        # 添加文字分析部分
        html_template += f"""
        <!-- 市场概况 -->
        <div class="section">
            <div class="text-content">
                {self._markdown_to_html(market_summary)}
            </div>
        </div>

        <!-- 核心指标表 -->
        <div class="section">
            <h2>📊 波动率核心指标</h2>
            <div class="chart-container">
                {chart_htmls.get('volatility_table', '<p>图表生成失败</p>')}
            </div>
        </div>

        <!-- 市场情绪面板 -->
        <div class="section">
            <h2>🎯 市场情绪面板</h2>
            <div class="chart-container">
                {chart_htmls.get('market_sentiment_panel', '<p>图表生成失败</p>')}
            </div>
        </div>

        <!-- 价格走势图 -->
        <div class="section">
            <h2>📈 股指期货价格走势</h2>
            <div class="chart-container">
                <div class="chart-title">IH/IF/IM 一年期价格走势对比</div>
                {chart_htmls.get('futures_price_trends', '<p>图表生成失败</p>')}
            </div>
        </div>

        <!-- 波动率分析 -->
        <div class="section">
            <div class="text-content">
                {self._markdown_to_html(volatility_analysis)}
            </div>
        </div>

        <!-- IV-HV对比图 -->
        <div class="section">
            <h2>📊 IV-HV波动率对比</h2>
            <div class="grid-container">
                <div class="chart-container">
                    <div class="chart-title">IV-HV走势对比</div>
                    {chart_htmls.get('iv_hv_trends', '<p>图表生成失败</p>')}
                </div>
                <div class="chart-container">
                    <div class="chart-title">IV-HV价差分析</div>
                    {chart_htmls.get('iv_hv_spread', '<p>图表生成失败</p>')}
                </div>
            </div>
        </div>

        <!-- 波动率深度分析 -->
        <div class="section">
            <h2>🌊 波动率深度分析</h2>
            <div class="grid-container">
                <div class="chart-container">
                    <div class="chart-title">波动率微笑曲线</div>
                    {chart_htmls.get('volatility_smile', '<p>图表生成失败</p>')}
                </div>
                <div class="chart-container">
                    <div class="chart-title">波动率期限结构</div>
                    {chart_htmls.get('term_structure', '<p>图表生成失败</p>')}
                </div>
            </div>
            <div class="chart-container">
                <div class="chart-title">3D波动率曲面</div>
                {chart_htmls.get('volatility_surface', '<p>图表生成失败</p>')}
            </div>
        </div>"""

        # 继续添加期权分析和其他图表
        html_template += f"""
        <!-- 期权市场分析 -->
        <div class="section">
            <div class="text-content">
                {self._markdown_to_html(option_analysis)}
            </div>
        </div>

        <!-- PCR和期权持仓 -->
        <div class="section">
            <h2>🎯 期权市场深度分析</h2>
            <div class="grid-container">
                <div class="chart-container">
                    <div class="chart-title">PCR走势图</div>
                    {chart_htmls.get('pcr_trends', '<p>图表生成失败</p>')}
                </div>
                <div class="chart-container">
                    <div class="chart-title">期权持仓分布</div>
                    {chart_htmls.get('position_distribution', '<p>图表生成失败</p>')}
                </div>
            </div>
        </div>

        <!-- 资金流向分析 -->
        <div class="section">
            <h2>💰 资金流向分析</h2>
            <div class="grid-container">
                <div class="chart-container">
                    <div class="chart-title">北向南向资金流向</div>
                    {chart_htmls.get('capital_flow', '<p>图表生成失败</p>')}
                </div>
                <div class="chart-container">
                    <div class="chart-title">两融规模变化</div>
                    {chart_htmls.get('margin_trading_scale', '<p>图表生成失败</p>')}
                </div>
            </div>
        </div>

        <!-- 投资建议 -->
        <div class="section">
            <h2>💡 投资策略建议</h2>
            <div class="text-content">
                <h3>🎯 短期策略</h3>
                <ul>
                    <li><strong>趋势跟踪</strong>: 基于价格走势和技术指标进行方向性交易</li>
                    <li><strong>波动率交易</strong>: 利用IV-HV价差进行期权套利</li>
                    <li><strong>资金流向</strong>: 关注北向资金动向，把握外资情绪</li>
                </ul>

                <h3>📈 中长期布局</h3>
                <ul>
                    <li><strong>价值投资</strong>: 关注基本面改善的标的</li>
                    <li><strong>主题投资</strong>: 把握政策导向和行业轮动机会</li>
                    <li><strong>风险管理</strong>: 利用期权工具进行组合保护</li>
                </ul>

                <h3>⚠️ 风险提示</h3>
                <ul>
                    <li>市场波动风险，注意仓位控制</li>
                    <li>政策变化风险，关注监管动态</li>
                    <li>流动性风险，避免过度集中持仓</li>
                    <li>期权交易风险，理解希腊字母影响</li>
                </ul>
            </div>
        </div>

        <!-- 页脚 -->
        <div class="footer">
            <p><strong>🏛️ 中国股指期货综合分析报告系统</strong></p>
            <p>📊 基于Wind API真实数据 | 🤖 AI智能分析 | 📈 专业图表可视化</p>
            <p>生成时间: {self.report_date.strftime('%Y年%m月%d日 %H:%M:%S')}</p>
            <p style="font-size: 0.9em; color: #999;">
                本报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。
            </p>
        </div>
    </div>

    <script>
        // 添加图表交互功能
        document.addEventListener('DOMContentLoaded', function() {{
            // 为所有图表添加响应式调整
            window.addEventListener('resize', function() {{
                var plots = document.querySelectorAll('.js-plotly-plot');
                plots.forEach(function(plot) {{
                    Plotly.Plots.resize(plot);
                }});
            }});

            console.log('📊 中国股指期货综合分析报告已加载完成');
            console.log('🎯 包含图表数量: {len(chart_htmls)}');
        }});
    </script>
</body>
</html>"""

        return html_template

    def _markdown_to_html(self, markdown_text):
        """简单的Markdown到HTML转换"""
        html = markdown_text

        # 转换标题
        html = html.replace('### ', '<h3>').replace('\n', '</h3>\n', 1) if '### ' in html else html
        html = html.replace('## ', '<h2>').replace('\n', '</h2>\n', 1) if '## ' in html else html

        # 转换列表
        lines = html.split('\n')
        in_list = False
        result_lines = []

        for line in lines:
            if line.strip().startswith('- '):
                if not in_list:
                    result_lines.append('<ul>')
                    in_list = True
                result_lines.append(f'<li>{line.strip()[2:]}</li>')
            else:
                if in_list:
                    result_lines.append('</ul>')
                    in_list = False
                if line.strip():
                    result_lines.append(f'<p>{line.strip()}</p>')
                else:
                    result_lines.append('<br>')

        if in_list:
            result_lines.append('</ul>')

        return '\n'.join(result_lines)
