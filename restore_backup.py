#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国股指期货报告系统 - 备份恢复工具
版本: v1.0
创建时间: 2025-08-03

此脚本用于恢复项目到特定的备份点
"""

import os
import sys
import subprocess
import argparse
from datetime import datetime

def run_command(command, cwd=None):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            cwd=cwd,
            capture_output=True, 
            text=True, 
            encoding='utf-8'
        )
        return result.returncode == 0, result.stdout, result.stderr
    except Exception as e:
        return False, "", str(e)

def check_git_repo():
    """检查是否在Git仓库中"""
    success, _, _ = run_command("git status")
    return success

def list_available_backups():
    """列出可用的备份点"""
    print("📋 可用的备份点:")
    print("-" * 50)
    
    # 列出所有标签
    success, output, _ = run_command("git tag -l")
    if success and output.strip():
        print("🏷️  标签备份:")
        for tag in output.strip().split('\n'):
            # 获取标签信息
            success2, tag_info, _ = run_command(f"git show {tag} --format='%ci %s' --no-patch")
            if success2:
                print(f"   {tag}: {tag_info.strip()}")
            else:
                print(f"   {tag}")
        print()
    
    # 列出最近的提交
    print("📝 最近的提交:")
    success, output, _ = run_command("git log --oneline -10")
    if success:
        for line in output.strip().split('\n'):
            print(f"   {line}")
    print()

def restore_to_backup(backup_point, create_branch=False, branch_name=None):
    """恢复到指定备份点"""
    print(f"🔄 开始恢复到备份点: {backup_point}")
    
    # 检查工作区是否干净
    success, output, _ = run_command("git status --porcelain")
    if success and output.strip():
        print("⚠️  工作区有未提交的更改:")
        print(output)
        response = input("是否要暂存这些更改? (y/n): ").lower()
        if response == 'y':
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            stash_message = f"自动暂存 - 恢复前备份 {timestamp}"
            success, _, error = run_command(f'git stash push -m "{stash_message}"')
            if success:
                print("✅ 更改已暂存")
            else:
                print(f"❌ 暂存失败: {error}")
                return False
        else:
            print("❌ 请先处理未提交的更改")
            return False
    
    # 执行恢复
    if create_branch and branch_name:
        # 创建新分支并切换
        success, _, error = run_command(f"git checkout -b {branch_name} {backup_point}")
        if success:
            print(f"✅ 已创建新分支 '{branch_name}' 并恢复到 {backup_point}")
        else:
            print(f"❌ 分支创建失败: {error}")
            return False
    else:
        # 直接切换到备份点
        success, _, error = run_command(f"git checkout {backup_point}")
        if success:
            print(f"✅ 已恢复到 {backup_point}")
        else:
            print(f"❌ 恢复失败: {error}")
            return False
    
    return True

def verify_restore():
    """验证恢复结果"""
    print("\n🔍 验证恢复结果...")
    
    # 检查当前分支/提交
    success, output, _ = run_command("git log --oneline -1")
    if success:
        print(f"📍 当前位置: {output.strip()}")
    
    # 检查关键文件
    key_files = [
        "requirements.txt",
        "main.py",
        "config/config_template.py",
        "modules/__init__.py"
    ]
    
    print("\n📁 关键文件检查:")
    all_files_exist = True
    for file_path in key_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (缺失)")
            all_files_exist = False
    
    if all_files_exist:
        print("\n🎉 恢复验证成功！")
        return True
    else:
        print("\n⚠️  部分文件缺失，请检查恢复结果")
        return False

def main():
    parser = argparse.ArgumentParser(description="中国股指期货报告系统 - 备份恢复工具")
    parser.add_argument("--list", "-l", action="store_true", help="列出可用的备份点")
    parser.add_argument("--restore", "-r", type=str, help="恢复到指定备份点")
    parser.add_argument("--branch", "-b", type=str, help="创建新分支名称")
    parser.add_argument("--verify", "-v", action="store_true", help="验证当前状态")
    
    args = parser.parse_args()
    
    print("🔧 中国股指期货报告系统 - 备份恢复工具")
    print("=" * 50)
    
    # 检查Git仓库
    if not check_git_repo():
        print("❌ 当前目录不是Git仓库")
        sys.exit(1)
    
    if args.list:
        list_available_backups()
    
    elif args.restore:
        backup_point = args.restore
        create_branch = bool(args.branch)
        branch_name = args.branch
        
        if restore_to_backup(backup_point, create_branch, branch_name):
            verify_restore()
        else:
            print("❌ 恢复失败")
            sys.exit(1)
    
    elif args.verify:
        verify_restore()
    
    else:
        # 交互式模式
        print("\n请选择操作:")
        print("1. 列出可用备份点")
        print("2. 恢复到备份点")
        print("3. 验证当前状态")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-3): ").strip()
        
        if choice == "1":
            list_available_backups()
        elif choice == "2":
            list_available_backups()
            backup_point = input("请输入要恢复的备份点: ").strip()
            if backup_point:
                create_branch_choice = input("是否创建新分支? (y/n): ").lower()
                if create_branch_choice == 'y':
                    branch_name = input("请输入新分支名称: ").strip()
                    if restore_to_backup(backup_point, True, branch_name):
                        verify_restore()
                else:
                    if restore_to_backup(backup_point, False, None):
                        verify_restore()
        elif choice == "3":
            verify_restore()
        elif choice == "0":
            print("👋 再见!")
        else:
            print("❌ 无效选择")

if __name__ == "__main__":
    main()
