#!/usr/bin/env python3
"""
仅运行置顶公众号抓取（跳过URL列表）
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.article_fetcher import ArticleFetcher
from modules.report_composer import ReportComposer
from datetime import datetime
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s | %(levelname)s | %(message)s')
logger = logging.getLogger(__name__)

def main():
    """主函数：仅运行置顶公众号抓取"""
    print("🔍 置顶公众号最新文章抓取")
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        # 初始化文章获取器
        print("📱 初始化文章获取器...")
        article_fetcher = ArticleFetcher()
        
        # 抓取置顶公众号文章
        print("\n🔄 开始抓取置顶公众号最新文章...")
        print("注意：此过程需要手动处理验证码")
        print("系统将自动打开浏览器，请按提示操作")
        
        # 询问用户是否继续
        confirm = input("\n是否继续抓取置顶公众号文章? (y/N): ").strip().lower()
        if confirm != 'y':
            print("❌ 用户取消操作")
            return False
        
        # 执行抓取
        print("\n📊 正在抓取...")
        wechat_articles = article_fetcher.fetch_wechat_articles()
        
        if not wechat_articles:
            print("⚠️  未抓取到任何文章")
            print("可能原因：")
            print("1. 时间窗内没有新文章")
            print("2. Chrome浏览器配置问题")
            print("3. 网络连接问题")
            return False
        
        # 显示抓取结果
        print(f"\n✅ 抓取完成！共获取 {len(wechat_articles)} 篇文章")
        print("\n📋 文章列表:")
        for i, article in enumerate(wechat_articles[:10], 1):  # 只显示前10篇
            title = article.get('title', 'Unknown')
            source = article.get('source', 'Unknown')
            print(f"  {i}. {source}《{title}》")
        
        if len(wechat_articles) > 10:
            print(f"  ... 还有 {len(wechat_articles) - 10} 篇文章")
        
        # 生成报告
        print(f"\n📝 开始生成基于最新文章的报告...")
        
        # 初始化报告编排器
        report_composer = ReportComposer()
        
        # 准备数据
        article_data = {'wechat': wechat_articles}
        chart_data = {}  # 暂时跳过图表生成
        
        # 生成报告
        report_path = report_composer.compose_report(
            chart_data=chart_data,
            article_data=article_data
        )
        
        if report_path:
            print(f"✅ 报告生成成功: {report_path}")
            
            # 显示报告统计
            try:
                with open(report_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    word_count = len(content)
                print(f"📊 报告统计:")
                print(f"  - 字数: {word_count}")
                print(f"  - 基于文章数: {len(wechat_articles)}")
                print(f"  - 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            except Exception as e:
                print(f"⚠️  无法读取报告统计: {e}")
        else:
            print("❌ 报告生成失败")
            return False
        
        print("\n🎉 置顶公众号抓取和报告生成完成！")
        return True
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        return False
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        logger.exception("详细错误信息:")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
