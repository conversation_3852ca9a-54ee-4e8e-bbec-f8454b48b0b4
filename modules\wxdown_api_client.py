#!/usr/bin/env python3
"""
微信文章导出工具API客户端 - 完全基于API的自动化抓取
"""

import os
import time
import logging
import requests
import json
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import yaml
import re

logger = logging.getLogger(__name__)

class WxDownAPIClient:
    """微信文章导出工具API客户端"""
    
    def __init__(self, token=None):
        self.base_url = "https://exporter.wxdown.online/dashboard/api"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json',
            'Referer': 'https://exporter.wxdown.online/dashboard/account'
        })
        
        if token:
            self.session.headers['Authorization'] = f'Bearer {token}'
        
        self.config = self._load_config()
        self.time_window = self._get_time_window()
        self.keywords = self.config.get('keywords', [])
        
        logger.info(f"初始化WxDown API客户端，时间窗: {self.time_window[0]} -> {self.time_window[1]}")
    
    def _load_config(self):
        """加载配置文件"""
        cfg_path = "config/wechat_sources.yaml"
        if not os.path.exists(cfg_path):
            return {}
        with open(cfg_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f) or {}
    
    def _get_time_window(self):
        """获取时间窗口"""
        now = datetime.now()
        
        # 工作日：当天15:00后
        # 周末：从周五15:00开始
        if now.weekday() < 5:  # 周一到周五
            start_time = now.replace(hour=15, minute=0, second=0, microsecond=0)
            if now < start_time:
                # 如果还没到15点，从昨天15点开始
                start_time = start_time - timedelta(days=1)
        else:  # 周末
            # 找到最近的周五
            days_since_friday = (now.weekday() - 4) % 7
            friday = now - timedelta(days=days_since_friday)
            start_time = friday.replace(hour=15, minute=0, second=0, microsecond=0)
        
        return start_time, now
    
    def get_articles_auto(self) -> List[Dict]:
        """自动获取文章（主入口）"""
        logger.info("开始自动获取微信文章...")
        
        # 尝试多种方法获取文章
        articles = []
        
        # 方法1: 直接API调用
        articles = self._try_direct_api()
        if articles:
            logger.info(f"直接API调用成功，获取 {len(articles)} 篇文章")
        else:
            # 方法2: 导出API
            articles = self._try_export_api()
            if articles:
                logger.info(f"导出API调用成功，获取 {len(articles)} 篇文章")
            else:
                # 方法3: 页面解析
                articles = self._try_page_parsing()
                logger.info(f"页面解析获取 {len(articles)} 篇文章")
        
        # 过滤和处理文章
        filtered_articles = self._process_articles(articles)
        
        logger.info(f"最终获取 {len(filtered_articles)} 篇符合条件的文章")
        return filtered_articles
    
    def _try_direct_api(self) -> List[Dict]:
        """尝试直接API调用"""
        try:
            start_time, end_time = self.time_window
            params = {
                'start_date': start_time.strftime('%Y-%m-%d'),
                'end_date': end_time.strftime('%Y-%m-%d'),
                'limit': 100
            }
            
            response = self.session.get(f"{self.base_url}/articles", params=params, timeout=30)
            logger.info(f"直接API调用: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if isinstance(data, list):
                        return data
                    elif isinstance(data, dict):
                        return data.get('articles', data.get('data', []))
                except json.JSONDecodeError:
                    return self._parse_articles_from_html(response.text)
            
        except Exception as e:
            logger.debug(f"直接API调用失败: {e}")
        
        return []
    
    def _try_export_api(self) -> List[Dict]:
        """尝试导出API"""
        try:
            start_time, end_time = self.time_window
            data = {
                'start_date': start_time.strftime('%Y-%m-%d'),
                'end_date': end_time.strftime('%Y-%m-%d'),
                'format': 'json',
                'accounts': self.config.get('starred_accounts', [])
            }
            
            response = self.session.post(f"{self.base_url}/export", json=data, timeout=60)
            logger.info(f"导出API调用: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    if isinstance(result, dict):
                        return result.get('articles', result.get('data', []))
                    elif isinstance(result, list):
                        return result
                except json.JSONDecodeError:
                    return self._parse_articles_from_html(response.text)
            
        except Exception as e:
            logger.debug(f"导出API调用失败: {e}")
        
        return []
    
    def _try_page_parsing(self) -> List[Dict]:
        """尝试页面解析"""
        try:
            # 获取主页面内容
            response = self.session.get("https://exporter.wxdown.online/dashboard/account", timeout=30)
            
            if response.status_code == 200:
                return self._parse_articles_from_html(response.text)
            
        except Exception as e:
            logger.debug(f"页面解析失败: {e}")
        
        return []
    
    def _parse_articles_from_html(self, html_content: str) -> List[Dict]:
        """从HTML内容解析文章信息"""
        articles = []
        try:
            # 查找微信文章链接
            mp_links = re.findall(r'https://mp\.weixin\.qq\.com/s/[A-Za-z0-9_-]+', html_content)
            
            # 尝试提取标题
            title_patterns = [
                r'<title>([^<]+)</title>',
                r'data-title="([^"]+)"',
                r'title["\']?\s*:\s*["\']([^"\']+)["\']',
                r'<h[1-6][^>]*>([^<]+)</h[1-6]>',
                r'class="[^"]*title[^"]*"[^>]*>([^<]+)<'
            ]
            
            titles = []
            for pattern in title_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE | re.DOTALL)
                titles.extend([title.strip() for title in matches if title.strip()])
            
            # 尝试提取发布时间
            time_patterns = [
                r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2}:\d{2})',
                r'(\d{4}-\d{2}-\d{2}\s+\d{2}:\d{2})',
                r'(\d{2}-\d{2}\s+\d{2}:\d{2})',
                r'(\d{2}:\d{2})'
            ]
            
            times = []
            for pattern in time_patterns:
                matches = re.findall(pattern, html_content)
                times.extend(matches)
            
            # 组合数据
            unique_links = list(set(mp_links))
            for i, url in enumerate(unique_links):
                title = titles[i] if i < len(titles) else f"微信文章 {i+1}"
                pub_time = times[i] if i < len(times) else datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                
                articles.append({
                    'title': title.strip(),
                    'url': url,
                    'source': '微信公众号',
                    'publish_time': pub_time,
                    'content': '',
                    'images': []
                })
            
            logger.info(f"从HTML解析到 {len(articles)} 篇文章")
            return articles
            
        except Exception as e:
            logger.error(f"解析文章HTML失败: {e}")
            return []
    
    def _process_articles(self, articles: List[Dict]) -> List[Dict]:
        """处理和过滤文章"""
        if not articles:
            return []
        
        # 时间过滤
        time_filtered = self._filter_articles_by_time(articles)
        
        # 关键词过滤
        keyword_filtered = self._filter_articles_by_keywords(time_filtered)
        
        # 去重
        deduplicated = self._deduplicate_articles(keyword_filtered)
        
        return deduplicated
    
    def _filter_articles_by_time(self, articles: List[Dict]) -> List[Dict]:
        """根据时间窗过滤文章"""
        start_time, end_time = self.time_window
        filtered_articles = []
        
        for article in articles:
            pub_time_str = article.get('publish_time', '')
            try:
                if pub_time_str:
                    # 支持多种时间格式
                    time_formats = [
                        '%Y-%m-%d %H:%M:%S',
                        '%Y-%m-%d %H:%M',
                        '%Y-%m-%d',
                        '%m-%d %H:%M',
                        '%H:%M'
                    ]
                    
                    pub_time = None
                    for fmt in time_formats:
                        try:
                            pub_time = datetime.strptime(pub_time_str, fmt)
                            # 补充缺失的日期信息
                            if fmt in ['%H:%M', '%m-%d %H:%M']:
                                pub_time = pub_time.replace(year=datetime.now().year)
                                if fmt == '%H:%M':
                                    pub_time = pub_time.replace(month=datetime.now().month, day=datetime.now().day)
                            break
                        except ValueError:
                            continue
                    
                    if pub_time and start_time <= pub_time <= end_time:
                        filtered_articles.append(article)
                else:
                    # 没有时间信息的文章默认包含
                    filtered_articles.append(article)
                    
            except Exception as e:
                logger.debug(f"时间解析失败: {pub_time_str}, {e}")
                filtered_articles.append(article)
        
        logger.info(f"时间过滤: {len(articles)} -> {len(filtered_articles)} 篇文章")
        return filtered_articles
    
    def _filter_articles_by_keywords(self, articles: List[Dict]) -> List[Dict]:
        """根据关键词过滤文章"""
        if not self.keywords:
            return articles
        
        filtered_articles = []
        for article in articles:
            title = article.get('title', '').lower()
            content = article.get('content', '').lower()
            
            # 检查标题或内容是否包含关键词
            for keyword in self.keywords:
                if keyword.lower() in title or keyword.lower() in content:
                    filtered_articles.append(article)
                    break
        
        logger.info(f"关键词过滤: {len(articles)} -> {len(filtered_articles)} 篇文章")
        return filtered_articles
    
    def _deduplicate_articles(self, articles: List[Dict]) -> List[Dict]:
        """去重文章"""
        seen_urls = set()
        unique_articles = []
        
        for article in articles:
            url = article.get('url', '')
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_articles.append(article)
        
        logger.info(f"去重处理: {len(articles)} -> {len(unique_articles)} 篇文章")
        return unique_articles

def main():
    """测试API客户端"""
    print("🔍 测试微信文章导出工具API客户端")
    
    client = WxDownAPIClient()
    articles = client.get_articles_auto()
    
    print(f"\n✅ 获取到 {len(articles)} 篇文章")
    for i, article in enumerate(articles[:5], 1):
        print(f"{i}. {article['title'][:50]}...")
        print(f"   URL: {article['url'][:60]}...")
        print(f"   时间: {article['publish_time']}")

if __name__ == "__main__":
    main()
