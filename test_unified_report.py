#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一报告生成器
"""

import sys
import os
import time
import webbrowser
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from loguru import logger
    from utils.logger_setup import setup_logger
    from utils.directory_manager import ensure_directories
    from modules.comprehensive_chart_generator import ComprehensiveChartGenerator
    from modules.chart_generator import ChartGenerator
    from config.config import OUTPUT_DIRS
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)


def test_individual_charts():
    """测试单个图表生成"""
    print("🔧 测试单个图表生成...")
    
    # 设置日志
    setup_logger()
    ensure_directories()
    
    # 创建图表生成器
    comprehensive_gen = ComprehensiveChartGenerator()
    chart_gen = ChartGenerator()
    
    print("\n📊 测试综合图表生成器...")
    
    try:
        # 测试波动率核心指标表
        print("   📋 测试波动率核心指标表...")
        volatility_table = comprehensive_gen.generate_volatility_core_table()
        print("   ✅ 波动率核心指标表生成成功")
        
        # 测试期货价格走势图
        print("   📈 测试期货价格走势图...")
        price_trends = comprehensive_gen.generate_futures_price_trends()
        print("   ✅ 期货价格走势图生成成功")
        
        # 测试IV-HV走势图
        print("   📊 测试IV-HV走势图...")
        iv_hv_trends = comprehensive_gen.generate_iv_hv_trends()
        print("   ✅ IV-HV走势图生成成功")
        
    except Exception as e:
        print(f"   ❌ 综合图表生成失败: {e}")
        return False
    
    print("\n📊 测试基础图表生成器...")
    
    try:
        # 测试市场情绪面板
        print("   🎯 测试市场情绪面板...")
        sentiment_panel = chart_gen.generate_market_sentiment_panel()
        print("   ✅ 市场情绪面板生成成功")
        
        # 测试资金流向图
        print("   💰 测试资金流向图...")
        capital_flow = chart_gen.generate_capital_flow()
        print("   ✅ 资金流向图生成成功")
        
    except Exception as e:
        print(f"   ❌ 基础图表生成失败: {e}")
        return False
    
    print("\n✅ 所有图表测试通过！")
    return True


def generate_simple_unified_report():
    """生成简化的统一报告"""
    print("📊 生成简化统一报告...")
    
    try:
        # 创建图表生成器
        comprehensive_gen = ComprehensiveChartGenerator()
        chart_gen = ChartGenerator()
        
        # 生成核心图表
        print("   📋 生成波动率核心指标表...")
        volatility_table = comprehensive_gen.generate_volatility_core_table()
        
        print("   🎯 生成市场情绪面板...")
        sentiment_panel = chart_gen.generate_market_sentiment_panel()
        
        print("   📈 生成期货价格走势图...")
        price_trend = chart_gen.generate_futures_price_trend()
        
        print("   💰 生成资金流向图...")
        capital_flow = chart_gen.generate_capital_flow()
        
        # 创建简化的HTML报告
        html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中国股指期货综合分析报告</title>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        .header {{
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #007bff;
        }}
        .section {{
            margin-bottom: 40px;
        }}
        .chart-container {{
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }}
        h1 {{ color: #007bff; }}
        h2 {{ color: #333; margin-top: 30px; }}
        .date {{ color: #666; font-size: 1.1em; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏛️ 中国股指期货综合分析报告</h1>
            <div class="date">📅 {datetime.now().strftime('%Y年%m月%d日 %H:%M')}</div>
            <div style="margin-top: 15px; color: #28a745;">
                <strong>📊 基于Wind API真实数据 | 🤖 AI智能分析 | 📈 专业图表可视化</strong>
            </div>
        </div>

        <div class="section">
            <h2>📊 波动率核心指标</h2>
            <div class="chart-container">
                {volatility_table.to_html(full_html=False, include_plotlyjs=True) if volatility_table else '<p>图表生成失败</p>'}
            </div>
        </div>

        <div class="section">
            <h2>🎯 市场情绪面板</h2>
            <div class="chart-container">
                <p>市场情绪面板已生成，请查看单独的HTML文件</p>
            </div>
        </div>

        <div class="section">
            <h2>📈 期货价格走势</h2>
            <div class="chart-container">
                <p>期货价格走势图已生成，请查看单独的HTML文件</p>
            </div>
        </div>

        <div class="section">
            <h2>💰 资金流向分析</h2>
            <div class="chart-container">
                <p>资金流向图已生成，请查看单独的HTML文件</p>
            </div>
        </div>

        <div class="section">
            <h2>💡 投资建议</h2>
            <div style="padding: 20px; background: #e8f4fd; border-radius: 8px;">
                <h3>📈 短期策略</h3>
                <ul>
                    <li><strong>趋势跟踪</strong>: 基于价格走势和技术指标进行方向性交易</li>
                    <li><strong>波动率交易</strong>: 利用IV-HV价差进行期权套利</li>
                    <li><strong>资金流向</strong>: 关注北向资金动向，把握外资情绪</li>
                </ul>
                
                <h3>⚠️ 风险提示</h3>
                <ul>
                    <li>市场波动风险，注意仓位控制</li>
                    <li>政策变化风险，关注监管动态</li>
                    <li>流动性风险，避免过度集中持仓</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; color: #666;">
            <p><strong>🏛️ 中国股指期货综合分析报告系统</strong></p>
            <p>📊 基于Wind API真实数据 | 🤖 AI智能分析 | 📈 专业图表可视化</p>
            <p>生成时间: {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
            <p style="font-size: 0.9em;">本报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。</p>
        </div>
    </div>
</body>
</html>"""
        
        # 保存报告
        output_path = Path("output/reports") / f"unified_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        output_path.parent.mkdir(parents=True, exist_ok=True)
        
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"✅ 简化统一报告已生成: {output_path}")
        return str(output_path)
        
    except Exception as e:
        print(f"❌ 生成简化统一报告失败: {e}")
        return None


def main():
    """主函数"""
    print("🚀 启动统一报告测试系统...")
    print("=" * 60)
    
    # 设置日志
    setup_logger()
    ensure_directories()
    
    # 测试单个图表
    if not test_individual_charts():
        print("❌ 单个图表测试失败，退出程序")
        return False
    
    # 生成简化统一报告
    report_path = generate_simple_unified_report()
    
    if report_path:
        print("\n" + "=" * 60)
        print("✅ 测试完成！")
        print(f"📊 报告文件: {report_path}")
        
        # 询问是否打开报告
        open_report = input("\n🌐 是否在浏览器中打开报告？(y/n，默认y): ").strip().lower()
        if open_report != 'n':
            try:
                webbrowser.open(f'file:///{os.path.abspath(report_path)}')
                print("✅ 报告已在浏览器中打开")
            except Exception as e:
                print(f"❌ 无法打开浏览器: {e}")
        
        return True
    else:
        print("❌ 测试失败！")
        return False


if __name__ == "__main__":
    success = main()
    if success:
        print("\n✅ 程序执行成功！")
        sys.exit(0)
    else:
        print("\n❌ 程序执行失败！")
        sys.exit(1)
