# 🚀 中国股指期货报告系统 - 跨平台部署指南

## 📋 目录
1. [代码备份和版本管理](#代码备份和版本管理)
2. [Mac到Windows迁移](#mac到windows迁移)
3. [Windows环境配置](#windows环境配置)
4. [Wind API配置](#wind-api配置)
5. [项目恢复和同步](#项目恢复和同步)
6. [最佳实践](#最佳实践)

---

## 🔄 代码备份和版本管理

### ✅ 已完成的备份
- **Git提交**: 已创建完整项目备份提交
- **版本标签**: `v1.0-backup` - 可用于精确恢复
- **备份时间**: 2025-08-03

### 📝 Git命令参考
```bash
# 查看当前状态
git status

# 查看提交历史
git log --oneline

# 查看所有标签
git tag -l

# 恢复到备份点
git checkout v1.0-backup

# 创建新分支从备份点开始
git checkout -b new-feature v1.0-backup
```

---

## 💻 Mac到Windows迁移步骤

### 1. 准备迁移文件
在Mac上执行：
```bash
# 创建项目压缩包（包含.git目录）
cd /path/to/research-report-output-system
tar -czf stock-futures-system-backup.tar.gz .

# 或者使用Git克隆（推荐）
git bundle create stock-futures-system.bundle --all
```

### 2. 传输方式选择
- **OneDrive同步**: 直接通过云盘同步（当前使用）
- **Git远程仓库**: 推送到GitHub/GitLab
- **USB传输**: 复制压缩包到移动设备

---

## 🖥️ Windows环境配置

### 1. Python环境安装
```cmd
# 下载并安装Python 3.8+
# 从 https://www.python.org/downloads/ 下载

# 验证安装
python --version
pip --version
```

### 2. 创建虚拟环境
```cmd
# 进入项目目录
cd C:\path\to\research-report-output-system

# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

### 3. 系统依赖安装
```cmd
# 安装Chrome浏览器（用于Selenium）
# 下载: https://www.google.com/chrome/

# 安装Git（如果需要）
# 下载: https://git-scm.com/download/win
```

---

## 📊 Wind API配置

### Windows上的Wind API安装

#### 1. 下载Wind API
- 访问Wind官网或联系Wind客服获取WindPy安装包
- 通常文件名为: `WindPy-xxx.whl`

#### 2. 安装步骤
```cmd
# 激活虚拟环境
venv\Scripts\activate

# 安装Wind API
pip install WindPy-xxx.whl

# 验证安装
python -c "import WindPy as w; print('Wind API安装成功')"
```

#### 3. Wind终端配置
```cmd
# 确保Wind终端已安装并登录
# Wind终端路径通常为: C:\Wind\Wind.NET.Client\WindNET.exe

# 测试连接
python test_wind_integration.py
```

---

## 🔄 项目恢复和同步

### 从Git备份恢复
```cmd
# 方法1: 克隆整个仓库
git clone /path/to/repository stock-futures-system
cd stock-futures-system

# 方法2: 从bundle恢复
git clone stock-futures-system.bundle stock-futures-system
cd stock-futures-system

# 恢复到特定备份点
git checkout v1.0-backup
```

### 配置文件设置
```cmd
# 复制配置模板
copy config\config_template.py config\config.py

# 编辑配置文件
notepad config\config.py
```

### 验证环境
```cmd
# 运行测试
python -m pytest tests/

# 运行演示
python run_demo.py

# 生成测试报告
python generate_daily_report.py
```

---

## 🔄 跨平台同步策略

### 1. Git工作流（推荐）
```bash
# 在Mac上
git add .
git commit -m "Mac上的更新"
git push origin main

# 在Windows上
git pull origin main
```

### 2. OneDrive同步注意事项
- 确保`.git`目录同步
- 避免同时在两个平台编辑
- 定期提交更改到Git

### 3. 配置文件管理
```bash
# 创建平台特定配置
config/config_mac.py
config/config_windows.py

# 使用环境变量选择配置
export PLATFORM=mac  # Mac上
set PLATFORM=windows  # Windows上
```

---

## 📁 目录结构对比

### Mac路径示例
```
/Users/<USER>/research-report-output-system/
├── config/
├── modules/
├── output/
└── ...
```

### Windows路径示例
```
C:\Users\<USER>\research-report-output-system\
├── config\
├── modules\
├── output\
└── ...
```

---

## ⚠️ 常见问题解决

### 1. 路径分隔符问题
```python
import os
# 使用os.path.join()而不是硬编码路径
file_path = os.path.join('output', 'reports', 'daily.html')
```

### 2. 编码问题
```python
# 明确指定UTF-8编码
with open(file_path, 'r', encoding='utf-8') as f:
    content = f.read()
```

### 3. Wind API连接问题
```python
# 检查Wind终端是否运行
import WindPy as w
w.start()  # 启动连接
if w.isconnected():
    print("Wind连接成功")
else:
    print("Wind连接失败，请检查终端")
```

---

## 🎯 最佳实践建议

### 对于编程初学者：

1. **版本控制习惯**
   - 每天工作结束前提交代码
   - 使用有意义的提交信息
   - 重要功能完成后创建标签

2. **环境管理**
   - 始终使用虚拟环境
   - 及时更新requirements.txt
   - 记录环境配置步骤

3. **跨平台开发**
   - 使用相对路径
   - 避免平台特定的代码
   - 定期在两个平台测试

4. **备份策略**
   - Git + 云盘双重备份
   - 重要节点创建标签
   - 保留多个历史版本

---

## 📞 技术支持

如果在迁移过程中遇到问题：
1. 检查Python和依赖版本
2. 验证Wind API安装
3. 查看错误日志文件
4. 参考项目文档和测试用例

---

**备注**: 此指南基于当前项目状态（v1.0-backup）创建，确保在迁移前已完成Git备份。
