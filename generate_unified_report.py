#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成统一的综合分析报告
整合所有图表和文字分析，生成单一的完整HTML报告
"""

import sys
import os
import time
import webbrowser
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from loguru import logger
    from utils.logger_setup import setup_logger
    from utils.directory_manager import ensure_directories
    from modules.unified_report_generator import UnifiedReportGenerator
    from config.config import OUTPUT_DIRS
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)


def main():
    """主函数"""
    try:
        print("🚀 启动中国股指期货统一综合分析报告生成系统...")
        print("=" * 80)
        
        # 设置日志
        setup_logger()
        logger.info("开始生成统一综合分析报告")
        
        # 确保输出目录存在
        ensure_directories()
        
        # 创建统一报告生成器
        print("📊 初始化统一报告生成器...")
        report_generator = UnifiedReportGenerator()
        
        # 显示系统信息
        print("\n📋 报告配置信息:")
        print(f"   📅 报告日期: {datetime.now().strftime('%Y年%m月%d日')}")
        print(f"   📅 数据期间: 一年期历史数据")
        print(f"   📈 分析合约: IH (上证50)、IF (沪深300)、IM (中证1000)")
        print(f"   📊 包含内容: 12种专业图表 + 深度文字分析")
        print(f"   💾 输出格式: 统一HTML综合报告")
        
        print("\n🔄 开始生成统一综合报告...")
        print("   📊 正在生成所有图表...")
        print("   📝 正在生成深度分析...")
        print("   🎨 正在整合HTML页面...")
        
        # 记录开始时间
        start_time = time.time()
        
        # 生成统一报告
        output_path = report_generator.generate_unified_report()
        
        # 计算耗时
        elapsed_time = time.time() - start_time
        
        print("\n" + "=" * 80)
        print("✅ 统一综合分析报告生成完成！")
        print("=" * 80)
        print(f"📊 报告文件: {output_path}")
        print(f"⏱️  生成耗时: {elapsed_time:.2f} 秒")
        print(f"📁 输出目录: {OUTPUT_DIRS['reports']}")
        
        print("\n📋 报告内容摘要:")
        print("   📈 市场概况与主要指数表现")
        print("   📊 波动率核心指标表")
        print("   🎯 市场情绪面板")
        print("   📈 股指期货价格走势图 (IH/IF/IM)")
        print("   📊 IV-HV波动率深度对比分析")
        print("   🌊 波动率微笑曲线与期限结构")
        print("   🌊 3D波动率曲面")
        print("   📊 PCR走势图 (Put/Call Ratio)")
        print("   🥧 期权持仓分布图")
        print("   💰 北向南向资金流向图")
        print("   📊 两融规模变化图")
        print("   💡 专业投资建议与风险提示")
        
        print("\n🌟 报告特色功能:")
        print("   📱 响应式设计 - 支持手机、平板、电脑查看")
        print("   🖱️  交互式图表 - 可缩放、悬停、筛选数据")
        print("   📊 专业分析 - 基于一年期数据的深度洞察")
        print("   🎨 美观布局 - 图文并茂的专业报告格式")
        print("   📈 实时数据 - 集成Wind API的真实市场数据")
        print("   🤖 AI分析 - 智能生成投资建议和风险提示")
        
        print("\n" + "=" * 80)
        
        # 询问是否在浏览器中打开
        try:
            choice = input("🌐 是否在浏览器中打开统一报告？(y/n，默认y): ").strip().lower()
            if choice in ['', 'y', 'yes']:
                print("🌐 正在浏览器中打开统一报告...")
                webbrowser.open(f'file:///{os.path.abspath(output_path)}')
                print("✅ 报告已在浏览器中打开")
                
                print("\n💡 使用提示:")
                print("   📖 完整阅读体验 - 图表与文字分析完美结合")
                print("   🖱️  交互操作 - 鼠标滚轮缩放，拖拽平移")
                print("   📊 数据探索 - 悬停查看详细数值")
                print("   📱 多设备支持 - 手机端自动适配布局")
                print("   💾 保存分享 - 可直接保存或分享给同事")
                
                print("\n📊 报告结构说明:")
                print("   1️⃣  市场概况 - 当日主要指数表现")
                print("   2️⃣  核心指标 - 波动率关键数据")
                print("   3️⃣  市场情绪 - 综合情绪面板")
                print("   4️⃣  价格分析 - 一年期走势图表")
                print("   5️⃣  波动率分析 - IV-HV深度对比")
                print("   6️⃣  期权分析 - PCR与持仓分布")
                print("   7️⃣  资金流向 - 北向南向资金动态")
                print("   8️⃣  投资建议 - 基于数据的策略建议")
            else:
                print("📄 报告已保存，您可以稍后查看")
        except KeyboardInterrupt:
            print("\n👋 用户取消操作")
        
        print("\n🎉 感谢使用中国股指期货统一综合分析报告系统！")
        print("💡 建议每日运行生成最新分析报告")
        
        return True
        
    except Exception as e:
        logger.error(f"生成统一报告时出错: {e}")
        print(f"\n❌ 报告生成失败: {e}")
        print("💡 请检查:")
        print("   1. Wind API连接状态")
        print("   2. 网络连接是否正常")
        print("   3. 输出目录权限")
        print("   4. 依赖包是否完整安装")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
