#!/usr/bin/env python3
"""
分析微信文章导出工具的API接口
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import json
import time
import requests

def analyze_network_requests():
    """分析网站的网络请求"""
    print("🔍 分析微信文章导出工具的API接口")
    print("=" * 60)
    
    driver = None
    try:
        # 设置Chrome选项，启用网络日志
        chrome_options = Options()
        chrome_binary_path = r"D:\待删软件\EasySpider_Windows_x64\EasySpider\resources\app\chrome_win64\chrome.exe"
        
        if os.path.exists(chrome_binary_path):
            chrome_options.binary_location = chrome_binary_path
        
        # 启用网络日志
        chrome_options.add_argument("--enable-logging")
        chrome_options.add_argument("--log-level=0")
        chrome_options.add_experimental_option('useAutomationExtension', False)
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        
        # 启用性能日志以捕获网络请求
        chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        service = Service(ChromeDriverManager().install())
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        print("✓ 浏览器启动成功，已启用网络监控")
        
        # 访问网站
        url = "https://exporter.wxdown.online/dashboard/account"
        print(f"🌐 访问: {url}")
        driver.get(url)
        
        # 等待页面加载
        time.sleep(5)
        
        print("📊 分析页面结构和API调用...")
        
        # 获取性能日志（包含网络请求）
        logs = driver.get_log('performance')
        api_requests = []
        
        for log in logs:
            message = json.loads(log['message'])
            if message['message']['method'] == 'Network.responseReceived':
                response = message['message']['params']['response']
                url = response['url']
                
                # 过滤API请求
                if any(keyword in url.lower() for keyword in ['api', 'ajax', 'json', 'data']):
                    api_requests.append({
                        'url': url,
                        'method': response.get('method', 'GET'),
                        'status': response['status'],
                        'mimeType': response.get('mimeType', '')
                    })
        
        print(f"\n🔍 发现 {len(api_requests)} 个API请求:")
        for i, req in enumerate(api_requests[:10], 1):  # 只显示前10个
            print(f"  {i}. {req['method']} {req['url']}")
            print(f"     状态: {req['status']}, 类型: {req['mimeType']}")
        
        # 分析页面元素
        print(f"\n📋 分析页面元素...")
        
        # 查找可能的API端点
        try:
            # 查找JavaScript文件中的API端点
            scripts = driver.find_elements(By.TAG_NAME, "script")
            print(f"发现 {len(scripts)} 个脚本文件")
            
            # 查找可能的数据容器
            data_elements = driver.find_elements(By.CSS_SELECTOR, "[data-*], .api-*, #api-*")
            print(f"发现 {len(data_elements)} 个数据相关元素")
            
        except Exception as e:
            print(f"页面元素分析失败: {e}")
        
        # 手动操作指导
        print(f"\n" + "="*60)
        print("🔧 请在浏览器中执行以下操作来捕获API调用:")
        print("1. 打开开发者工具 (F12)")
        print("2. 切换到 Network 标签页")
        print("3. 刷新页面或执行操作（添加公众号、获取文章等）")
        print("4. 观察XHR/Fetch请求")
        print("5. 记录API端点和请求格式")
        print("="*60)
        
        input("完成API分析后，按回车键继续...")
        
        # 再次获取日志
        new_logs = driver.get_log('performance')
        new_api_requests = []
        
        for log in new_logs[len(logs):]:  # 只处理新的日志
            message = json.loads(log['message'])
            if message['message']['method'] == 'Network.responseReceived':
                response = message['message']['params']['response']
                url = response['url']
                
                if any(keyword in url.lower() for keyword in ['api', 'ajax', 'json', 'data']):
                    new_api_requests.append({
                        'url': url,
                        'method': response.get('method', 'GET'),
                        'status': response['status'],
                        'mimeType': response.get('mimeType', '')
                    })
        
        if new_api_requests:
            print(f"\n🆕 新发现 {len(new_api_requests)} 个API请求:")
            for i, req in enumerate(new_api_requests, 1):
                print(f"  {i}. {req['method']} {req['url']}")
        
        return api_requests + new_api_requests
        
    except Exception as e:
        print(f"❌ API分析失败: {e}")
        return []
    finally:
        if driver:
            try:
                driver.quit()
                print("🔧 浏览器已关闭")
            except:
                pass

def test_common_api_endpoints():
    """测试常见的API端点"""
    print(f"\n🧪 测试常见API端点...")
    
    base_url = "https://exporter.wxdown.online"
    common_endpoints = [
        "/api/accounts",
        "/api/articles",
        "/api/export",
        "/api/dashboard",
        "/api/user",
        "/api/data",
        "/dashboard/api/accounts",
        "/dashboard/api/articles"
    ]
    
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Accept': 'application/json, text/plain, */*',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8'
    }
    
    for endpoint in common_endpoints:
        try:
            url = base_url + endpoint
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                print(f"✅ {endpoint} - 状态: {response.status_code}")
                try:
                    data = response.json()
                    print(f"   响应类型: JSON, 键: {list(data.keys()) if isinstance(data, dict) else 'Array'}")
                except:
                    print(f"   响应类型: {response.headers.get('content-type', 'Unknown')}")
            elif response.status_code == 401:
                print(f"🔐 {endpoint} - 需要认证 (401)")
            elif response.status_code == 403:
                print(f"🚫 {endpoint} - 访问被拒绝 (403)")
            elif response.status_code == 404:
                print(f"❌ {endpoint} - 未找到 (404)")
            else:
                print(f"⚠️  {endpoint} - 状态: {response.status_code}")
                
        except requests.exceptions.Timeout:
            print(f"⏰ {endpoint} - 请求超时")
        except requests.exceptions.ConnectionError:
            print(f"🔌 {endpoint} - 连接错误")
        except Exception as e:
            print(f"❌ {endpoint} - 错误: {e}")

def generate_api_integration_plan(api_requests):
    """生成API集成方案"""
    print(f"\n📋 API集成方案")
    print("=" * 60)
    
    if api_requests:
        print("基于发现的API请求，建议的集成方案:")
        print()
        print("1. 认证机制:")
        print("   - 分析登录API调用")
        print("   - 获取认证token或session")
        print("   - 实现自动登录功能")
        print()
        print("2. 公众号管理:")
        print("   - 查找添加公众号的API")
        print("   - 实现批量添加功能")
        print("   - 获取已配置公众号列表")
        print()
        print("3. 文章获取:")
        print("   - 找到文章列表API")
        print("   - 实现时间过滤")
        print("   - 批量下载文章内容")
        print()
        print("4. 数据导出:")
        print("   - 使用导出API获取数据")
        print("   - 解析返回的文章数据")
        print("   - 转换为系统需要的格式")
    else:
        print("未发现明显的API端点，建议方案:")
        print()
        print("1. 页面自动化:")
        print("   - 使用Selenium模拟用户操作")
        print("   - 自动添加公众号")
        print("   - 自动触发导出功能")
        print()
        print("2. 数据提取:")
        print("   - 解析页面DOM结构")
        print("   - 提取文章链接和元数据")
        print("   - 实现增量更新")
        print()
        print("3. 混合方案:")
        print("   - 结合API调用和页面自动化")
        print("   - 优先使用API，降级到页面操作")

def main():
    """主函数"""
    print("🔍 微信文章导出工具API分析")
    
    # 1. 分析网络请求
    api_requests = analyze_network_requests()
    
    # 2. 测试常见端点
    test_common_api_endpoints()
    
    # 3. 生成集成方案
    generate_api_integration_plan(api_requests)
    
    print(f"\n🎯 分析完成！")
    print("下一步: 基于分析结果优化 wxdown_fetcher.py 模块")

if __name__ == "__main__":
    main()
