#!/usr/bin/env python3
"""
微信文章导出工具集成模块 - 基于API的自动化抓取
使用 https://exporter.wxdown.online/dashboard/api 作为数据源
"""

import os
import time
import logging
import requests
import json
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Optional
import yaml
import re

logger = logging.getLogger(__name__)

class WxDownAPIClient:
    """微信文章导出工具API客户端"""

    def __init__(self, token=None):
        self.base_url = "https://exporter.wxdown.online/dashboard/api"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Content-Type': 'application/json',
            'Referer': 'https://exporter.wxdown.online/dashboard/account'
        })

        if token:
            self.session.headers['Authorization'] = f'Bearer {token}'

        self.config = self._load_config()
        self.time_window = self._get_time_window()
        self.keywords = self.config.get('keywords', [])
        
    def _load_config(self):
        """加载配置文件"""
        cfg_path = "config/wechat_sources.yaml"
        if not os.path.exists(cfg_path):
            return {}
        with open(cfg_path, "r", encoding="utf-8") as f:
            return yaml.safe_load(f) or {}
    
    def _get_time_window(self):
        """获取时间窗口"""
        now = datetime.now()
        
        # 工作日：当天15:00后
        # 周末：从周五15:00开始
        if now.weekday() < 5:  # 周一到周五
            start_time = now.replace(hour=15, minute=0, second=0, microsecond=0)
            if now < start_time:
                # 如果还没到15点，从昨天15点开始
                start_time = start_time - timedelta(days=1)
        else:  # 周末
            # 找到最近的周五
            days_since_friday = (now.weekday() - 4) % 7
            friday = now - timedelta(days=days_since_friday)
            start_time = friday.replace(hour=15, minute=0, second=0, microsecond=0)
        
        return start_time, now

    def get_accounts(self) -> Optional[List[Dict]]:
        """获取账号列表"""
        try:
            response = self.session.get(f"{self.base_url}/accounts", timeout=30)
            logger.info(f"获取账号列表: {response.status_code}")

            if response.status_code == 200:
                # 尝试解析JSON
                try:
                    data = response.json()
                    return data if isinstance(data, list) else data.get('accounts', [])
                except json.JSONDecodeError:
                    # 如果不是JSON，尝试解析HTML或其他格式
                    return self._parse_accounts_from_html(response.text)
            else:
                logger.warning(f"获取账号列表失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"获取账号列表异常: {e}")
            return None

    def get_articles(self, account_id=None, start_date=None, end_date=None, limit=50) -> Optional[List[Dict]]:
        """获取文章列表"""
        try:
            params = {'limit': limit}
            if account_id:
                params['account_id'] = account_id
            if start_date:
                params['start_date'] = start_date.strftime('%Y-%m-%d') if isinstance(start_date, datetime) else start_date
            if end_date:
                params['end_date'] = end_date.strftime('%Y-%m-%d') if isinstance(end_date, datetime) else end_date

            response = self.session.get(f"{self.base_url}/articles", params=params, timeout=30)
            logger.info(f"获取文章列表: {response.status_code}, 参数: {params}")

            if response.status_code == 200:
                try:
                    data = response.json()
                    return data if isinstance(data, list) else data.get('articles', [])
                except json.JSONDecodeError:
                    return self._parse_articles_from_html(response.text)
            else:
                logger.warning(f"获取文章列表失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"获取文章列表异常: {e}")
            return None

    def export_articles(self, filters=None) -> Optional[Dict]:
        """导出文章"""
        try:
            data = filters or {}

            # 添加时间过滤
            start_time, end_time = self.time_window
            data.update({
                'start_date': start_time.strftime('%Y-%m-%d'),
                'end_date': end_time.strftime('%Y-%m-%d'),
                'format': 'json'
            })

            response = self.session.post(f"{self.base_url}/export", json=data, timeout=60)
            logger.info(f"导出文章: {response.status_code}")

            if response.status_code == 200:
                try:
                    return response.json()
                except json.JSONDecodeError:
                    return self._parse_export_from_html(response.text)
            else:
                logger.warning(f"导出文章失败: {response.status_code}")
                return None

        except Exception as e:
            logger.error(f"导出文章异常: {e}")
            return None

    def _parse_accounts_from_html(self, html_content: str) -> List[Dict]:
        """从HTML内容解析账号信息"""
        accounts = []
        try:
            # 使用正则表达式查找账号信息
            # 这里需要根据实际HTML结构调整
            account_patterns = [
                r'data-account-id="([^"]+)"[^>]*>([^<]+)',
                r'account["\']?\s*:\s*["\']([^"\']+)["\']',
                r'name["\']?\s*:\s*["\']([^"\']+)["\']'
            ]

            for pattern in account_patterns:
                matches = re.findall(pattern, html_content, re.IGNORECASE)
                for match in matches:
                    if isinstance(match, tuple) and len(match) >= 2:
                        accounts.append({
                            'id': match[0],
                            'name': match[1]
                        })
                    elif isinstance(match, str):
                        accounts.append({
                            'id': match,
                            'name': match
                        })

            # 去重
            seen = set()
            unique_accounts = []
            for account in accounts:
                key = account.get('id', '') + account.get('name', '')
                if key not in seen:
                    seen.add(key)
                    unique_accounts.append(account)

            logger.info(f"从HTML解析到 {len(unique_accounts)} 个账号")
            return unique_accounts

        except Exception as e:
            logger.error(f"解析账号HTML失败: {e}")
            return []
        """初始化浏览器"""
        try:
            logger.info("初始化浏览器访问微信文章导出工具...")
            chrome_options = Options()
            
            # 指定Chrome浏览器路径
            chrome_binary_path = r"D:\待删软件\EasySpider_Windows_x64\EasySpider\resources\app\chrome_win64\chrome.exe"
            if os.path.exists(chrome_binary_path):
                chrome_options.binary_location = chrome_binary_path
                logger.info(f"使用指定的Chrome路径: {chrome_binary_path}")
            
            if self.headless:
                chrome_options.add_argument("--headless=new")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--disable-blink-features=AutomationControlled")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
            chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
            chrome_options.add_experimental_option("useAutomationExtension", False)

            service = Service(ChromeDriverManager().install())
            self._driver = webdriver.Chrome(service=service, options=chrome_options)
            self._driver.execute_script(
                "Object.defineProperty(navigator, 'webdriver', {get: () => undefined})"
            )
            self._wait = WebDriverWait(self._driver, 30)
            
            logger.info("✓ 浏览器初始化成功")
            return True
        except Exception as e:
            logger.error(f"浏览器初始化失败: {e}")
            return False
    
    def open_wxdown_dashboard(self):
        """打开微信文章导出工具仪表板"""
        if not self._driver:
            if not self.setup_browser():
                return False
        
        try:
            url = "https://exporter.wxdown.online/dashboard/account"
            logger.info(f"打开微信文章导出工具: {url}")
            self._driver.get(url)
            
            print("\n" + "="*60)
            print("📱 已打开微信文章导出工具")
            print("请在浏览器中执行以下操作：")
            print("1. 登录或注册账号（如需要）")
            print("2. 添加要监控的公众号")
            print("3. 设置导出参数")
            print("4. 完成后返回此窗口继续")
            print("="*60)
            
            return True
            
        except Exception as e:
            logger.error(f"打开微信文章导出工具失败: {e}")
            return False
    
    def fetch_articles_from_wxdown(self, account_names=None):
        """从微信文章导出工具获取文章（完全自动化）"""
        if not account_names:
            account_names = self.config.get('starred_accounts', [])

        if not account_names:
            logger.warning("未配置公众号列表")
            return []

        logger.info(f"开始自动化获取 {len(account_names)} 个公众号的文章")

        # 打开工具页面
        if not self.open_wxdown_dashboard():
            return []

        articles = []

        try:
            # 自动化操作流程
            articles = self._automated_article_extraction()

            if not articles:
                # 如果自动化失败，尝试智能页面解析
                articles = self._intelligent_page_parsing()

            if not articles:
                # 最后尝试通用DOM解析
                articles = self._generic_dom_parsing()

        except Exception as e:
            logger.error(f"自动化获取文章失败: {e}")

        # 过滤和处理文章
        filtered_articles = self._filter_and_process_articles(articles)

        logger.info(f"✓ 从微信文章导出工具获取到 {len(filtered_articles)} 篇文章")
        return filtered_articles

    def _automated_article_extraction(self):
        """自动化文章提取"""
        logger.info("尝试自动化文章提取...")
        articles = []

        try:
            # 等待页面完全加载
            self._wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
            time.sleep(3)

            # 尝试查找导出按钮或文章列表
            export_selectors = [
                "button[contains(text(), '导出')]",
                ".export-btn", ".download-btn",
                "[data-action='export']", "[data-action='download']"
            ]

            for selector in export_selectors:
                try:
                    export_btn = self._driver.find_element(By.CSS_SELECTOR, selector)
                    if export_btn.is_displayed():
                        logger.info(f"找到导出按钮: {selector}")
                        export_btn.click()
                        time.sleep(2)
                        break
                except:
                    continue

            # 查找文章列表容器
            list_selectors = [
                ".article-list", ".post-list", ".content-list",
                "[data-list='articles']", ".data-table tbody tr"
            ]

            for selector in list_selectors:
                try:
                    article_container = self._driver.find_element(By.CSS_SELECTOR, selector)
                    articles = self._extract_articles_from_container(article_container)
                    if articles:
                        logger.info(f"从容器 {selector} 提取到 {len(articles)} 篇文章")
                        break
                except:
                    continue

        except Exception as e:
            logger.debug(f"自动化提取失败: {e}")

        return articles

    def _intelligent_page_parsing(self):
        """智能页面解析"""
        logger.info("尝试智能页面解析...")
        articles = []

        try:
            # 查找所有可能包含文章信息的元素
            potential_article_elements = self._driver.find_elements(
                By.CSS_SELECTOR,
                "tr, .item, .card, .post, .article, [data-id], [data-url]"
            )

            logger.info(f"发现 {len(potential_article_elements)} 个潜在文章元素")

            for element in potential_article_elements:
                try:
                    article_data = self._extract_article_from_element(element)
                    if article_data and self._is_valid_article(article_data):
                        articles.append(article_data)

                        if len(articles) >= 50:  # 限制数量
                            break

                except Exception as e:
                    logger.debug(f"解析元素失败: {e}")
                    continue

        except Exception as e:
            logger.debug(f"智能解析失败: {e}")

        return articles

    def _generic_dom_parsing(self):
        """通用DOM解析"""
        logger.info("尝试通用DOM解析...")
        articles = []

        try:
            # 查找页面中所有的微信文章链接
            page_source = self._driver.page_source

            # 使用正则表达式查找微信文章链接
            import re
            mp_links = re.findall(r'https://mp\.weixin\.qq\.com/s/[A-Za-z0-9_-]+', page_source)

            logger.info(f"在页面源码中发现 {len(mp_links)} 个微信文章链接")

            # 去重
            unique_links = list(set(mp_links))

            for i, url in enumerate(unique_links[:30], 1):  # 限制30篇
                try:
                    # 尝试从页面中提取对应的标题
                    title = self._extract_title_for_url(url)

                    article = {
                        'title': title or f"文章 {i}",
                        'url': url,
                        'source': '微信公众号',
                        'publish_time': datetime.now().strftime('%Y-%m-%d %H:%M'),
                        'content': '',
                        'images': []
                    }
                    articles.append(article)

                except Exception as e:
                    logger.debug(f"处理链接失败: {e}")
                    continue

        except Exception as e:
            logger.debug(f"通用解析失败: {e}")

        return articles
    
    def run(self):
        """运行微信文章导出工具抓取"""
        start_time, end_time = self.time_window
        logger.info(f"时间窗口: {start_time.strftime('%Y-%m-%d %H:%M')} → {end_time.strftime('%Y-%m-%d %H:%M')}")
        
        articles = self.fetch_articles_from_wxdown()
        
        # 过滤时间窗内的文章
        filtered_articles = []
        for article in articles:
            # 这里需要根据实际的时间格式进行解析和过滤
            # 暂时返回所有文章
            filtered_articles.append(article)
        
        return filtered_articles
    
    def cleanup(self):
        """清理资源"""
        try:
            if self._driver:
                self._driver.quit()
                logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"清理浏览器失败: {e}")

def main():
    """测试微信文章导出工具"""
    print("🔍 测试微信文章导出工具")
    
    fetcher = WxDownFetcher(headless=False)
    
    try:
        articles = fetcher.run()
        print(f"\n✅ 获取到 {len(articles)} 篇文章")
        
        for i, article in enumerate(articles[:5], 1):
            print(f"{i}. {article['title'][:50]}...")
            
    except KeyboardInterrupt:
        print("\n❌ 用户中断")
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
    finally:
        fetcher.cleanup()

if __name__ == "__main__":
    main()
