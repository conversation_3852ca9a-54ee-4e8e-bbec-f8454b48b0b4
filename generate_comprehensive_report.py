#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成综合分析报告
包含所有图表的集成HTML页面，针对IH、IF、IM分别制作，数据长度为一年期
"""

import sys
import os
import time
import webbrowser
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from loguru import logger
    from utils.logger_setup import setup_logger
    from utils.directory_manager import ensure_directories
    from modules.comprehensive_chart_generator import ComprehensiveChartGenerator
    from config.config import OUTPUT_DIRS
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)


def main():
    """主函数"""
    try:
        print("🚀 启动中国股指期货综合分析报告生成系统...")
        print("=" * 60)
        
        # 设置日志
        setup_logger()
        logger.info("开始生成综合分析报告")
        
        # 确保输出目录存在
        ensure_directories()
        
        # 创建综合图表生成器
        print("📊 初始化综合图表生成器...")
        generator = ComprehensiveChartGenerator()
        
        # 显示系统信息
        print("\n📋 报告配置信息:")
        print(f"   📅 数据期间: 一年期 ({(datetime.now().replace(year=datetime.now().year-1)).strftime('%Y-%m-%d')} 至 {datetime.now().strftime('%Y-%m-%d')})")
        print(f"   📈 分析合约: IH (上证50)、IF (沪深300)、IM (中证1000)")
        print(f"   📊 图表类型: 9种专业图表")
        print(f"   💾 输出格式: 集成HTML页面")
        
        # 开始生成
        print("\n🔄 开始生成综合分析报告...")
        start_time = time.time()
        
        # 生成综合HTML报告
        output_path = generator.generate_comprehensive_html()
        
        # 计算耗时
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 60)
        print("✅ 综合分析报告生成完成！")
        print("=" * 60)
        print(f"📊 报告文件: {output_path}")
        print(f"⏱️  生成耗时: {duration:.2f} 秒")
        print(f"📁 输出目录: {OUTPUT_DIRS['images']}")
        
        # 显示报告内容摘要
        print("\n📋 报告内容摘要:")
        print("   📈 波动率核心指标表")
        print("   📊 股指期货价格走势图 (IH/IF/IM)")
        print("   📈 IV-HV走势对比图 (分合约显示)")
        print("   📊 PCR走势图 (Put/Call Ratio)")
        print("   😊 波动率微笑曲线 (分合约显示)")
        print("   📈 波动率期限结构")
        print("   🌊 波动率曲面 (3D)")
        print("   📊 IV-HV价差图")
        print("   🥧 期权持仓分布图")
        
        # 询问是否打开报告
        print("\n" + "=" * 60)
        open_report = input("🌐 是否在浏览器中打开综合报告？(y/n，默认y): ").strip().lower()
        
        if open_report != 'n':
            try:
                print("🌐 正在浏览器中打开综合报告...")
                file_url = f"file://{Path(output_path).absolute()}"
                webbrowser.open(file_url)
                print("✅ 报告已在浏览器中打开")
                
                # 等待一下让浏览器加载
                time.sleep(2)
                
                print("\n💡 使用提示:")
                print("   🖱️  鼠标滚轮: 缩放图表")
                print("   🖱️  拖拽: 平移图表")
                print("   🖱️  悬停: 查看详细数据")
                print("   🖱️  点击图例: 显示/隐藏数据系列")
                print("   📱 响应式设计: 支持手机和平板查看")
                
            except Exception as e:
                print(f"❌ 打开浏览器失败: {e}")
                print(f"💡 请手动打开文件: {output_path}")
        
        print("\n🎉 感谢使用中国股指期货综合分析报告系统！")
        return True
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断程序执行")
        return False
    except Exception as e:
        logger.error(f"生成综合报告失败: {e}")
        print(f"\n❌ 生成综合报告失败: {e}")
        return False


def show_help():
    """显示帮助信息"""
    print("""
📊 中国股指期货综合分析报告生成系统

🎯 功能特点:
   • 一年期历史数据分析
   • IH、IF、IM三大合约全覆盖
   • 9种专业图表集成显示
   • 响应式HTML页面设计
   • 交互式图表体验

📈 包含图表:
   1. 波动率核心指标表
   2. 股指期货价格走势图
   3. IV-HV走势对比图
   4. PCR走势图
   5. 波动率微笑曲线
   6. 波动率期限结构
   7. 波动率曲面 (3D)
   8. IV-HV价差图
   9. 期权持仓分布图

🚀 使用方法:
   python3 generate_comprehensive_report.py

📁 输出文件:
   output/images/comprehensive_report.html

💡 系统要求:
   • Python 3.8+
   • 已安装依赖包 (pip install -r requirements.txt)
   • 可选: Wind API (用于真实数据)
""")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
    else:
        success = main()
        sys.exit(0 if success else 1)
