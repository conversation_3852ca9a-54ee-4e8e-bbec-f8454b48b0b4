#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Aggregate multiple article summaries and compose payloads for V15.2 templates.
This is a light initial version to wire the pipeline; will be refined.
"""
from __future__ import annotations

from typing import List, Dict
from loguru import logger


def aggregate_summaries(summaries: List[Dict]) -> Dict:
    if not summaries:
        return {
            "core_narrative": "暂无",
            "evidences": [],
            "weights_table": [],
            "risks": [],
            "appendix_images": [],
            "appendix_texts": [],
        }

    # 取前3条权重最高的要点
    weights_rows = []
    for s in summaries:
        for w in s.get("weights", []):
            weights_rows.append({"source": s.get("source"), "title": s.get("title"), **w})
    weights_rows.sort(key=lambda x: x.get("评分", 0), reverse=True)

    core = "；".join([f"{r['类型']} {r['评分']}分" for r in weights_rows[:3]]) or "多因素影响下的震荡市"

    # 证据：每篇文章选取一个关键点
    evidences = []
    for s in summaries[:5]:
        kp = s.get("key_points", ["无要点"])[0]
        evidences.append({"标题": s.get("title"), "来源": s.get("source"), "要点": kp})

    # 附录
    appendix_images = []
    appendix_texts = []
    for i, s in enumerate(summaries, 1):
        for img in s.get("images", []):
            appendix_images.append(f"图片{i}-{img}: 来自《{s.get('title')}》")
        appendix_texts.append(f"文本{i}: 《{s.get('title')}》— {s.get('key_points', ['无'])[0]}")

    return {
        "core_narrative": core,
        "evidences": evidences,
        "weights_table": weights_rows[:10],
        "risks": list({r for s in summaries for r in s.get("risks", [])})[:5],
        "appendix_images": appendix_images,
        "appendix_texts": appendix_texts,
        "per_index_impact": {  # 粗略聚合
            "SH50": "; ".join([s.get("index_impact", {}).get("SH50", {}).get("direction", "") for s in summaries])[:80],
            "HS300": "; ".join([s.get("index_impact", {}).get("HS300", {}).get("direction", "") for s in summaries])[:80],
            "CSI1000": "; ".join([s.get("index_impact", {}).get("CSI1000", {}).get("direction", "") for s in summaries])[:80],
        }
    }

