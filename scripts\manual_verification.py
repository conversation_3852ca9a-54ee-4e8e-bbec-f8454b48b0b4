#!/usr/bin/env python3
"""
手动验证搜狗微信抓取功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from modules.wechat_fetcher import WeChatFetcher
from datetime import datetime, timedelta
import time

def main():
    """手动验证搜狗微信功能"""
    print("🔍 手动验证搜狗微信抓取功能")
    print("=" * 60)
    
    try:
        # 初始化WeChatFetcher（非无头模式，便于观察）
        fetcher = WeChatFetcher(headless=False)
        
        # 设置24小时时间窗
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)
        fetcher.time_window = (start_time, end_time)
        
        print(f"时间窗: {start_time.strftime('%Y-%m-%d %H:%M')} → {end_time.strftime('%Y-%m-%d %H:%M')}")
        
        # 测试一个简单的公众号
        test_account = "华泰期货研究院"
        print(f"\n🔄 测试公众号: {test_account}")
        
        # 手动打开搜狗微信搜索
        print("\n📱 正在打开搜狗微信搜索页面...")
        success = fetcher._open_sogou_search(test_account)
        
        if not success:
            print("❌ 无法打开搜狗微信搜索页面")
            return False
        
        # 等待用户手动操作
        print("\n" + "="*60)
        print("🔧 请在浏览器中执行以下操作：")
        print("1. 如果出现验证码，请手动完成验证")
        print("2. 确认搜索结果中显示了目标公众号")
        print("3. 点击进入公众号页面")
        print("4. 确认能看到文章列表")
        print("="*60)
        
        input("完成上述操作后，按回车键继续...")
        
        # 尝试发现文章链接
        print(f"\n🔍 尝试发现文章链接...")
        urls = fetcher.discover_article_urls(test_account)
        
        print(f"发现 {len(urls)} 个文章链接:")
        for i, url in enumerate(urls[:5], 1):  # 只显示前5个
            print(f"  {i}. {url}")
        
        if urls:
            print(f"\n✅ 成功发现文章链接！")
            
            # 尝试抓取第一篇文章
            print(f"\n📄 尝试抓取第一篇文章...")
            first_url = urls[0]
            article = fetcher.fetch_article(first_url)
            
            if article:
                print(f"✅ 文章抓取成功！")
                print(f"  标题: {article.get('title', 'Unknown')}")
                print(f"  来源: {article.get('source', 'Unknown')}")
                print(f"  发布时间: {article.get('publish_time', 'Unknown')}")
                print(f"  正文长度: {len(article.get('content', ''))}")
                return True
            else:
                print(f"❌ 文章抓取失败")
                return False
        else:
            print(f"⚠️  未发现文章链接")
            
            # 调试信息
            print(f"\n🔧 调试信息:")
            print(f"  当前页面URL: {fetcher._driver.current_url}")
            print(f"  页面标题: {fetcher._driver.title}")
            
            # 检查页面源码中是否包含文章链接
            page_source = fetcher._driver.page_source
            mp_links = page_source.count('mp.weixin.qq.com')
            print(f"  页面中包含 {mp_links} 个微信文章链接")
            
            if mp_links == 0:
                print(f"  可能原因:")
                print(f"    1. 搜索结果中没有找到目标公众号")
                print(f"    2. 需要手动点击进入公众号页面")
                print(f"    3. 页面被反爬虫机制阻止")
            
            return False
        
    except KeyboardInterrupt:
        print("\n❌ 用户中断操作")
        return False
    except Exception as e:
        print(f"\n❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理浏览器
        try:
            if 'fetcher' in locals() and hasattr(fetcher, '_driver') and fetcher._driver:
                input("\n按回车键关闭浏览器...")
                fetcher._driver.quit()
                print("🔧 浏览器已关闭")
        except:
            pass

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 验证结果: {'成功' if success else '失败'}")
    sys.exit(0 if success else 1)
