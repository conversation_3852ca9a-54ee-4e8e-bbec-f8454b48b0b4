#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中国股指期货报告系统 - 跨平台同步检查工具
版本: v1.0
创建时间: 2025-08-03

此脚本用于检查项目在不同平台间的同步状态
"""

import os
import sys
import platform
import subprocess
import hashlib
from pathlib import Path

def get_system_info():
    """获取系统信息"""
    return {
        'platform': platform.system(),
        'platform_release': platform.release(),
        'platform_version': platform.version(),
        'architecture': platform.machine(),
        'hostname': platform.node(),
        'python_version': platform.python_version(),
    }

def run_command(command):
    """执行命令并返回结果"""
    try:
        result = subprocess.run(
            command, 
            shell=True, 
            capture_output=True, 
            text=True, 
            encoding='utf-8'
        )
        return result.returncode == 0, result.stdout.strip(), result.stderr.strip()
    except Exception as e:
        return False, "", str(e)

def check_git_status():
    """检查Git状态"""
    print("📊 Git状态检查:")
    print("-" * 30)
    
    # 检查是否是Git仓库
    success, _, _ = run_command("git status")
    if not success:
        print("❌ 不是Git仓库")
        return False
    
    # 当前分支
    success, branch, _ = run_command("git branch --show-current")
    if success:
        print(f"🌿 当前分支: {branch}")
    
    # 最新提交
    success, commit, _ = run_command("git log --oneline -1")
    if success:
        print(f"📝 最新提交: {commit}")
    
    # 工作区状态
    success, status, _ = run_command("git status --porcelain")
    if success:
        if status:
            print("⚠️  工作区有未提交更改:")
            for line in status.split('\n'):
                print(f"   {line}")
        else:
            print("✅ 工作区干净")
    
    # 远程同步状态
    success, _, _ = run_command("git remote")
    if success:
        success, ahead_behind, _ = run_command("git status -b --porcelain")
        if "ahead" in ahead_behind or "behind" in ahead_behind:
            print("⚠️  与远程仓库不同步")
        else:
            print("✅ 与远程仓库同步")
    else:
        print("ℹ️  无远程仓库配置")
    
    print()
    return True

def check_python_environment():
    """检查Python环境"""
    print("🐍 Python环境检查:")
    print("-" * 30)
    
    # Python版本
    print(f"📍 Python版本: {sys.version}")
    
    # 虚拟环境检查
    if hasattr(sys, 'real_prefix') or (hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix):
        print("✅ 运行在虚拟环境中")
        print(f"📁 虚拟环境路径: {sys.prefix}")
    else:
        print("⚠️  未使用虚拟环境")
    
    # 检查关键依赖
    key_packages = [
        'pandas', 'numpy', 'plotly', 'matplotlib', 
        'selenium', 'requests', 'PyYAML'
    ]
    
    print("\n📦 关键依赖检查:")
    for package in key_packages:
        try:
            __import__(package)
            # 获取版本
            try:
                module = __import__(package)
                version = getattr(module, '__version__', 'unknown')
                print(f"   ✅ {package}: {version}")
            except:
                print(f"   ✅ {package}: 已安装")
        except ImportError:
            print(f"   ❌ {package}: 未安装")
    
    print()

def check_project_structure():
    """检查项目结构"""
    print("📁 项目结构检查:")
    print("-" * 30)
    
    required_dirs = [
        'config', 'modules', 'templates', 'utils', 'tests', 'output'
    ]
    
    required_files = [
        'main.py', 'requirements.txt', 'README.md',
        'config/config_template.py', 'modules/__init__.py'
    ]
    
    print("📂 必需目录:")
    for dir_name in required_dirs:
        if os.path.isdir(dir_name):
            print(f"   ✅ {dir_name}/")
        else:
            print(f"   ❌ {dir_name}/ (缺失)")
    
    print("\n📄 必需文件:")
    for file_name in required_files:
        if os.path.isfile(file_name):
            print(f"   ✅ {file_name}")
        else:
            print(f"   ❌ {file_name} (缺失)")
    
    print()

def check_configuration():
    """检查配置文件"""
    print("⚙️  配置文件检查:")
    print("-" * 30)
    
    config_files = [
        'config/config.py',
        'config/config_template.py',
        'config/system_rules.yaml'
    ]
    
    for config_file in config_files:
        if os.path.isfile(config_file):
            print(f"   ✅ {config_file}")
            # 检查文件大小
            size = os.path.getsize(config_file)
            print(f"      📏 大小: {size} bytes")
        else:
            print(f"   ❌ {config_file} (缺失)")
    
    print()

def calculate_file_hash(file_path):
    """计算文件哈希值"""
    try:
        with open(file_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    except:
        return None

def check_file_integrity():
    """检查关键文件完整性"""
    print("🔍 文件完整性检查:")
    print("-" * 30)
    
    key_files = [
        'main.py', 'requirements.txt',
        'modules/chart_generator.py',
        'modules/report_composer.py'
    ]
    
    for file_path in key_files:
        if os.path.isfile(file_path):
            file_hash = calculate_file_hash(file_path)
            size = os.path.getsize(file_path)
            print(f"   📄 {file_path}")
            print(f"      🔐 MD5: {file_hash}")
            print(f"      📏 大小: {size} bytes")
        else:
            print(f"   ❌ {file_path} (缺失)")
    
    print()

def generate_sync_report():
    """生成同步报告"""
    system_info = get_system_info()
    
    print("📋 同步状态报告")
    print("=" * 50)
    
    print(f"🖥️  系统: {system_info['platform']} {system_info['platform_release']}")
    print(f"🏠 主机: {system_info['hostname']}")
    print(f"🐍 Python: {system_info['python_version']}")
    print(f"📅 检查时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 执行各项检查
    check_git_status()
    check_python_environment()
    check_project_structure()
    check_configuration()
    check_file_integrity()
    
    print("🎯 同步建议:")
    print("-" * 30)
    
    # 检查是否有未提交更改
    success, status, _ = run_command("git status --porcelain")
    if success and status:
        print("1. 提交当前更改: git add . && git commit -m '更新'")
    
    # 检查远程同步
    success, _, _ = run_command("git remote")
    if success:
        print("2. 推送到远程仓库: git push")
        print("3. 在其他平台拉取: git pull")
    else:
        print("2. 考虑添加远程仓库进行同步")
    
    print("4. 确保两个平台的Python环境一致")
    print("5. 定期运行此检查脚本验证同步状态")
    print()

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == '--report':
        # 生成详细报告
        generate_sync_report()
    else:
        # 快速检查
        print("🔧 中国股指期货报告系统 - 同步检查")
        print("=" * 50)
        
        system_info = get_system_info()
        print(f"🖥️  当前平台: {system_info['platform']}")
        print()
        
        # 快速检查
        git_ok = check_git_status()
        if git_ok:
            check_python_environment()
            check_project_structure()
        
        print("💡 提示: 使用 'python sync_check.py --report' 生成详细报告")

if __name__ == "__main__":
    main()
