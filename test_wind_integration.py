#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Wind API集成测试脚本
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_wind_import():
    """测试Wind API导入"""
    print("🔍 测试Wind API导入...")
    
    try:
        from WindPy import w
        print("✅ WindPy导入成功")
        return True
    except ImportError as e:
        print(f"❌ WindPy导入失败: {e}")
        return False

def test_wind_connection():
    """测试Wind连接"""
    print("\n🔗 测试Wind连接...")
    
    try:
        from WindPy import w
        
        # 启动Wind
        ret = w.start()
        print(f"Wind启动结果: {ret}")
        
        # 检查连接状态
        connected = w.isconnected()
        print(f"Wind连接状态: {connected}")
        
        if connected:
            print("✅ Wind API连接成功")
            return True
        else:
            print("❌ Wind API未连接")
            return False
            
    except Exception as e:
        print(f"❌ Wind连接测试失败: {e}")
        return False

def test_wind_data():
    """测试Wind数据获取"""
    print("\n📊 测试Wind数据获取...")
    
    try:
        from WindPy import w
        
        # 测试基础数据获取
        print("测试股票基础信息...")
        ret = w.wss("000001.SZ,000002.SZ", "sec_name,close", "")
        print(f"股票信息: {ret}")
        
        if ret.ErrorCode == 0:
            print("✅ 股票数据获取成功")
        else:
            print(f"❌ 股票数据获取失败: ErrorCode={ret.ErrorCode}")
            return False
        
        # 测试期货数据
        print("\n测试期货数据...")
        ret = w.wss("IF.CFE,IH.CFE,IM.CFE", "sec_name,close", "")
        print(f"期货信息: {ret}")
        
        if ret.ErrorCode == 0:
            print("✅ 期货数据获取成功")
        else:
            print(f"❌ 期货数据获取失败: ErrorCode={ret.ErrorCode}")
            return False
        
        # 测试历史数据
        print("\n测试历史数据...")
        ret = w.wsd("000300.SH", "close,volume", "2025-07-01", "2025-08-03", "")
        print(f"历史数据条数: {len(ret.Data[0]) if ret.ErrorCode == 0 else 0}")
        
        if ret.ErrorCode == 0:
            print("✅ 历史数据获取成功")
            return True
        else:
            print(f"❌ 历史数据获取失败: ErrorCode={ret.ErrorCode}")
            return False
            
    except Exception as e:
        print(f"❌ Wind数据测试失败: {e}")
        return False

def test_chart_generator():
    """测试图表生成器Wind集成"""
    print("\n📈 测试图表生成器Wind集成...")
    
    try:
        from modules.chart_generator import ChartGenerator
        
        # 创建图表生成器
        generator = ChartGenerator()
        
        if generator.wind_connected:
            print("✅ 图表生成器Wind连接成功")
            
            # 测试生成一张图表
            print("测试生成市场情绪面板...")
            result = generator.generate_market_sentiment_panel()
            
            if result:
                print("✅ 使用Wind数据生成图表成功")
                return True
            else:
                print("❌ 图表生成失败")
                return False
        else:
            print("⚠️ 图表生成器使用模拟数据模式")
            return True
            
    except Exception as e:
        print(f"❌ 图表生成器测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 Wind API集成测试")
    print("=" * 50)
    
    tests = [
        ("Wind API导入", test_wind_import),
        ("Wind连接", test_wind_connection),
        ("Wind数据获取", test_wind_data),
        ("图表生成器集成", test_chart_generator)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📋 测试结果汇总:")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！Wind API集成成功！")
        print("\n💡 现在可以运行完整系统:")
        print("   python main.py")
    else:
        print("⚠️ 部分测试失败，请检查Wind API配置")
        print("\n💡 确保:")
        print("   1. Wind API应用已安装并登录")
        print("   2. Wind终端账号有效")
        print("   3. 网络连接正常")

if __name__ == "__main__":
    main()
