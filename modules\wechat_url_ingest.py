#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Utility to read WeChat article URLs from input/article_urls.txt and fetch them
via WeChatFetcher (requests + Selenium fallback as needed).
"""
from __future__ import annotations

from pathlib import Path
from typing import List
from loguru import logger

from modules.wechat_fetcher import WeChatFetcher


def read_urls(file_path: str = "input/article_urls.txt") -> List[str]:
    p = Path(file_path)
    if not p.exists():
        logger.warning("未找到 input/article_urls.txt，跳过URL导入")
        return []
    urls = []
    for line in p.read_text(encoding="utf-8").splitlines():
        line = line.strip()
        if line and line.startswith("http"):
            urls.append(line)
    return urls


def fetch_from_urls(urls: List[str]) -> List[dict]:
    if not urls:
        return []
    fetcher = WeChatFetcher(headless=False)
    results = []
    for u in urls:
        art = fetcher.fetch_article(u)
        if art:
            results.append(art)
    return results

