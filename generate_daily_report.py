#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成集成图表的日报
将所有图表和分析文字集成到一个完整的HTML日报中
"""

import sys
import os
import time
import webbrowser
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from loguru import logger
    from utils.logger_setup import setup_logger
    from utils.directory_manager import ensure_directories
    from modules.daily_report_with_charts import DailyReportWithCharts
    from config.config import OUTPUT_DIRS
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保已安装所有依赖: pip install -r requirements.txt")
    sys.exit(1)


def main():
    """主函数"""
    try:
        print("🚀 启动中国股指期货日报生成系统...")
        print("=" * 70)
        
        # 设置日志
        setup_logger()
        logger.info("开始生成集成图表的日报")
        
        # 确保输出目录存在
        ensure_directories()
        
        # 创建日报生成器
        print("📊 初始化日报生成器...")
        report_generator = DailyReportWithCharts()
        
        # 显示系统信息
        print("\n📋 日报配置信息:")
        print(f"   📅 报告日期: {datetime.now().strftime('%Y年%m月%d日')}")
        print(f"   📅 数据期间: 一年期历史数据")
        print(f"   📈 分析合约: IH (上证50)、IF (沪深300)、IM (中证1000)")
        print(f"   📊 包含内容: 9种专业图表 + 深度文字分析")
        print(f"   💾 输出格式: 完整HTML日报")
        
        # 开始生成
        print("\n🔄 开始生成集成日报...")
        print("   📊 正在生成图表...")
        print("   📝 正在生成分析文字...")
        print("   🎨 正在整合HTML页面...")
        
        start_time = time.time()
        
        # 生成综合日报
        output_path = report_generator.generate_comprehensive_daily_report()
        
        # 计算耗时
        end_time = time.time()
        duration = end_time - start_time
        
        print("\n" + "=" * 70)
        print("✅ 集成日报生成完成！")
        print("=" * 70)
        print(f"📊 日报文件: {output_path}")
        print(f"⏱️  生成耗时: {duration:.2f} 秒")
        print(f"📁 输出目录: {OUTPUT_DIRS['reports']}")
        
        # 显示日报内容摘要
        print("\n📋 日报内容摘要:")
        print("   📈 市场概况与主要指数表现")
        print("   📊 波动率核心指标表")
        print("   📈 股指期货价格走势图 (IH/IF/IM)")
        print("   📊 PCR走势图 (Put/Call Ratio)")
        print("   🌊 波动率深度分析 (IV-HV对比、微笑曲线、期限结构)")
        print("   🌊 3D波动率曲面")
        print("   🎯 期权市场分析 (持仓分布、交易策略)")
        print("   💡 专业投资建议与风险提示")
        
        # 显示特色功能
        print("\n🌟 日报特色功能:")
        print("   📱 响应式设计 - 支持手机、平板、电脑查看")
        print("   🖱️  交互式图表 - 可缩放、悬停、筛选数据")
        print("   📊 专业分析 - 基于一年期数据的深度洞察")
        print("   🎨 美观布局 - 图文并茂的专业报告格式")
        print("   📈 实时数据 - 集成Wind API的真实市场数据")
        
        # 询问是否打开日报
        print("\n" + "=" * 70)
        open_report = input("🌐 是否在浏览器中打开日报？(y/n，默认y): ").strip().lower()
        
        if open_report != 'n':
            try:
                print("🌐 正在浏览器中打开日报...")
                file_url = f"file://{Path(output_path).absolute()}"
                webbrowser.open(file_url)
                print("✅ 日报已在浏览器中打开")
                
                # 等待一下让浏览器加载
                time.sleep(2)
                
                print("\n💡 使用提示:")
                print("   📖 完整阅读体验 - 图表与文字分析完美结合")
                print("   🖱️  交互操作 - 鼠标滚轮缩放，拖拽平移")
                print("   📊 数据探索 - 悬停查看详细数值")
                print("   📱 多设备支持 - 手机端自动适配布局")
                print("   💾 保存分享 - 可直接保存或分享给同事")
                
                print("\n📊 日报结构说明:")
                print("   1️⃣  市场概况 - 当日主要指数表现")
                print("   2️⃣  核心指标 - 波动率关键数据")
                print("   3️⃣  价格分析 - 一年期走势图表")
                print("   4️⃣  波动率分析 - IV-HV深度对比")
                print("   5️⃣  期权分析 - PCR与持仓分布")
                print("   6️⃣  投资建议 - 基于数据的策略建议")
                
            except Exception as e:
                print(f"❌ 打开浏览器失败: {e}")
                print(f"💡 请手动打开文件: {output_path}")
        
        print("\n🎉 感谢使用中国股指期货日报生成系统！")
        print("💡 建议每日运行生成最新分析报告")
        return True
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断程序执行")
        return False
    except Exception as e:
        logger.error(f"生成日报失败: {e}")
        print(f"\n❌ 生成日报失败: {e}")
        return False


def show_help():
    """显示帮助信息"""
    print("""
📊 中国股指期货集成日报生成系统

🎯 功能特点:
   • 图文并茂的完整日报格式
   • 一年期历史数据深度分析
   • IH、IF、IM三大合约全覆盖
   • 9种专业图表 + 文字分析
   • 响应式HTML设计
   • 交互式图表体验

📈 日报内容:
   1. 市场概况与指数表现
   2. 波动率核心指标表
   3. 股指期货价格走势分析
   4. IV-HV对比与价差分析
   5. PCR情绪指标分析
   6. 波动率微笑与期限结构
   7. 3D波动率曲面展示
   8. 期权持仓分布分析
   9. 专业投资策略建议

🚀 使用方法:
   python3 generate_daily_report.py

📁 输出文件:
   output/reports/daily_report_with_charts_YYYYMMDD_HHMMSS.html

💡 系统要求:
   • Python 3.8+
   • 已安装依赖包 (pip install -r requirements.txt)
   • 可选: Wind API (用于真实数据)

🌟 特色功能:
   • 专业的金融分析报告格式
   • 图表与文字分析完美结合
   • 支持多设备查看 (手机/平板/电脑)
   • 可直接保存或分享
""")


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] in ['-h', '--help', 'help']:
        show_help()
    else:
        success = main()
        sys.exit(0 if success else 1)
