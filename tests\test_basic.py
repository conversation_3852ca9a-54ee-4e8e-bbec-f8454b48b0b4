#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础功能测试
"""

import sys
import pytest
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_imports():
    """测试基础模块导入"""
    try:
        from config.config import validate_config
        from utils.directory_manager import ensure_directories
        from utils.logger_setup import setup_logger
        from modules.chart_generator import ChartGenerator
        from modules.article_fetcher import ArticleFetcher
        from modules.report_composer import ReportComposer
        assert True
    except ImportError as e:
        pytest.fail(f"模块导入失败: {e}")


def test_config_validation():
    """测试配置验证"""
    from config.config import validate_config
    
    try:
        validate_config()
        assert True
    except Exception as e:
        pytest.fail(f"配置验证失败: {e}")


def test_directory_creation():
    """测试目录创建"""
    from utils.directory_manager import ensure_directories
    
    try:
        result = ensure_directories()
        assert result is True
    except Exception as e:
        pytest.fail(f"目录创建失败: {e}")


def test_logger_setup():
    """测试日志设置"""
    from utils.logger_setup import setup_logger
    
    try:
        logger = setup_logger()
        logger.info("测试日志消息")
        assert True
    except Exception as e:
        pytest.fail(f"日志设置失败: {e}")


if __name__ == "__main__":
    pytest.main([__file__])
