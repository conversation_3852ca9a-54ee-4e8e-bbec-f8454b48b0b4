#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Validate Plan B: fetch WeChat articles from input/article_urls.txt only,
then run the analysis+report pipeline, and print outputs.
Usage:
  python scripts/validate_plan_b.py [--no-selenium]
"""
from __future__ import annotations

import argparse
from pathlib import Path
from loguru import logger

# 让脚本可以导入项目根目录下的包
import sys, os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

from utils.directory_manager import ensure_directories
from modules.wechat_fetcher import WeChatFetcher
from modules.report_composer import ReportComposer


def read_urls(path: str = "input/article_urls.txt"):
    p = Path(path)
    if not p.exists():
        logger.error(f"未找到URL列表: {p}")
        return []
    return [l.strip() for l in p.read_text(encoding="utf-8").splitlines() if l.strip()]


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--no-selenium", action="store_true", help="禁用Selenium兜底（仅requests抓取）")
    args = parser.parse_args()

    ensure_directories()

    urls = read_urls()
    if not urls:
        logger.error("URL列表为空，退出")
        return 2

    fetcher = WeChatFetcher(headless=False)
    if args.no_selenium:
        fetcher.config.setdefault("options", {})["use_selenium_render_fallback"] = False
        logger.info("本次验证禁用Selenium兜底，若HTML不完整将跳过该篇。")

    results = []
    for u in urls:
        art = fetcher.fetch_article(u)
        if art:
            results.append(art)
        else:
            logger.warning(f"抓取失败/不完整: {u}")

    logger.info(f"共抓取成功 {len(results)}/{len(urls)} 篇")

    # 组织为 article_data 交给 ReportComposer
    article_data = {"wechat": results}
    chart_data = {}  # 暂无图表，ReportComposer会给出默认洞察

    rc = ReportComposer()
    out = rc.compose_report(chart_data=chart_data, article_data=article_data)
    if out:
        print("\n=== 验证结果 ===")
        print(f"报告输出: {out['file_path']}")
        print(f"字数: {out['content_length']}")
        print("前3篇文章:")
        for a in results[:3]:
            print(f"- {a['source']}《{a['title']}》: {a.get('publish_time','')}")
        return 0
    return 1


if __name__ == "__main__":
    raise SystemExit(main())

