#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
报告编排生成模块
负责整合量化洞察、舆情摘要，使用AI生成最终报告
"""

import os
import json
from datetime import datetime
from pathlib import Path
# 支持Gemini（google-generativeai）
import google.generativeai as genai
from loguru import logger

from utils.directory_manager import get_output_path


class ReportComposer:
    """报告编排器"""

    def __init__(self):
        """初始化报告编排器"""
        self.openai_client = None
        self.setup_openai()

    def setup_openai(self):
        """设置Gemini客户端（兼容OpenAI接口）"""
        try:
            # 从配置中获取API密钥
            from config.config import GEMINI_CONFIG

            if not GEMINI_CONFIG.get("api_key") or GEMINI_CONFIG["api_key"] == "your_api_key_here":
                logger.warning("Gemini API密钥未设置，将使用模拟报告生成")
                return False

            # 设置代理（如果环境变量中有配置）
            self._setup_proxy()

            # 设置Gemini客户端
            import google.generativeai as genai
            genai.configure(api_key=GEMINI_CONFIG["api_key"])
            self.gemini_model = genai.GenerativeModel(GEMINI_CONFIG.get("model", "gemini-1.5-pro"))
            self.openai_client = self  # 兼容性标记

            logger.info("✓ Gemini客户端设置完成")
            return True

        except Exception as e:
            logger.error(f"Gemini设置失败: {e}")
            return False

    def _setup_proxy(self):
        """设置代理配置，支持自动检测系统代理环境变量"""
        import os

        # 检测系统代理环境变量
        proxy_vars = ['HTTPS_PROXY', 'HTTP_PROXY', 'https_proxy', 'http_proxy']
        proxy_url = None

        for var in proxy_vars:
            proxy_url = os.environ.get(var)
            if proxy_url:
                logger.info(f"检测到代理配置: {var}={proxy_url}")
                break

        if proxy_url:
            try:
                # 测试代理连接
                self._test_proxy_connection(proxy_url)
                logger.info("✓ 代理连接测试成功")
            except Exception as e:
                logger.warning(f"代理连接测试失败: {e}")
                logger.info("将尝试直连，如遇网络问题请检查代理配置")
        else:
            logger.info("未检测到代理配置，使用直连")

    def _test_proxy_connection(self, proxy_url):
        """测试代理连接是否可用"""
        import requests
        import urllib.parse

        # 解析代理URL
        parsed = urllib.parse.urlparse(proxy_url)
        if not parsed.scheme:
            proxy_url = f"http://{proxy_url}"

        proxies = {
            'http': proxy_url,
            'https': proxy_url
        }

        # 测试连接Google服务
        try:
            response = requests.get(
                'https://www.google.com',
                proxies=proxies,
                timeout=10,
                headers={'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}
            )
            if response.status_code == 200:
                return True
        except Exception as e:
            raise Exception(f"代理测试失败: {e}")

        return False

    def summarize_articles(self, article_data):
        """使用AI总结文章内容"""
        logger.info("正在总结舆情文章...")

        try:
            # 整理文章内容
            articles_text = ""

            if 'wechat' in article_data:
                for article in article_data['wechat']:
                    articles_text += f"标题: {article['title']}\n"
                    articles_text += f"来源: {article['source']}\n"
                    articles_text += f"内容: {article['content']}\n"
                    articles_text += "-" * 30 + "\n"

            if 'douyin' in article_data and article_data['douyin']:
                # 读取抖音内容文件
                douyin_file = article_data['douyin']['file_path']
                if Path(douyin_file).exists():
                    with open(douyin_file, 'r', encoding='utf-8') as f:
                        articles_text += f.read()

            if not articles_text.strip():
                logger.warning("没有文章内容可供总结")
                return self.generate_mock_summary()

            # 使用Gemini总结（优先）
            if self.openai_client:
                summary = self.gemini_generate(self._build_summary_prompt(articles_text)) or self.generate_mock_summary()
            else:
                summary = self.generate_mock_summary()

            logger.info("✓ 文章总结完成")
            return summary

        except Exception as e:
            logger.error(f"文章总结失败: {e}")
            return self.generate_mock_summary()
    def _build_summary_prompt(self, text: str) -> str:
        return f"""
你是一位期权策略师。请严格基于下面文章内容，输出“核心事件摘要/市场情绪/关键数据/对股指期货影响”的四段总结，每段均给出与A股传导的明确表述：

{text}

输出格式：
1. 核心事件摘要：
2. 市场情绪分析：
3. 关键数据提取：
4. 对股指期货市场的影响：
"""


    def ai_summarize_text(self, text):
        """使用AI总结文本（Gemini）"""
        try:
            prompt = self._build_summary_prompt(text)
            if hasattr(self, 'gemini_model'):
                out = self.gemini_generate(prompt)
                return out or self.generate_mock_summary()
            return self.generate_mock_summary()
        except Exception as e:
            logger.error(f"Gemini总结失败: {e}")
            return self.generate_mock_summary()

    # ---- Gemini based summarization ----
    def gemini_generate(self, prompt: str, max_tokens: int = 1500, temperature: float = 0.3) -> str:
        # 增加短超时与有限重试，提升不稳定网络下的成功率
        try:
            if not hasattr(self, 'gemini_model'):
                return ""
            from config.config import GEMINI_CONFIG
            import time
            last_err = None
            for attempt in range(3):  # 最多3次
                try:
                    resp = self.gemini_model.generate_content(
                        prompt,
                        generation_config={
                            'temperature': temperature,
                            'max_output_tokens': min(max_tokens, GEMINI_CONFIG.get('max_tokens', 4000))
                        },
                        request_options={
                            'timeout': 90  # 每次调用90s超时
                        }
                    )
                    return getattr(resp, 'text', '') or ''
                except Exception as inner:
                    last_err = inner
                    time.sleep(1.5 * (attempt + 1))  # 线性回退
            if last_err:
                raise last_err
            return ""
        except Exception as e:
            logger.error(f"Gemini调用失败: {e}")
            return ""

    def generate_mock_summary(self):
        """生成模拟总结"""
        return """
        **核心事件摘要：**
        1. A股三大指数全线收跌，上证指数跌0.94%，市场情绪转向谨慎
        2. 美国7月非农数据爆冷，新增就业仅7.3万人，远低于预期
        3. 全球降息预期升温，9月降息概率飙升至75.5%
        4. 期权隐含波动率处于高位，IV-HV分位值均在70%以上
        5. 北向资金净流出45.67亿元，外资情绪偏谨慎

        **市场情绪分析：**
        市场整体情绪从乐观转向谨慎，投资者对经济复苏预期有所降温。
        机构普遍认为短期将进入震荡调整，但中长期仍看好政策底支撑。

        **关键数据提取：**
        - PMI数据不及预期，经济基本面承压
        - 两市成交额萎缩至1.95万亿元
        - 期权PCR指标显示情绪偏向乐观
        - 美股暴跌，道指跌超600点

        **对股指期货市场影响：**
        短期下行压力增大，但全球流动性宽松预期提供支撑。
        期权高波动率为卖方策略提供机会，建议关注熊市价差和卖出宽跨式策略。
        """

    def integrate_chart_insights(self, chart_data):
        """整合图表洞察"""
        logger.info("正在整合图表分析洞察...")

        insights = []

        for chart_name, chart_info in chart_data.items():
            if 'insights' in chart_info:
                insights.append(f"**{chart_name}**: {chart_info['insights']}")

        if not insights:
            insights = [
                "**市场情绪面板**: 市场整体呈现下跌格局，资金流出明显",
                "**期货价格走势**: 三大股指期货呈现分化走势，IM波动最大"
            ]

        return "\n".join(insights)

    def generate_final_report(self, chart_insights, article_summary, agg: dict | None = None):
        """生成最终报告"""
        logger.info("正在生成最终报告...")

        try:
            # 读取报告模板（优先 Gemini 版）
            template_path = Path("项目参考文档/最终生成报告模版_gemini.md")
            if not template_path.exists():
                template_path = Path("项目参考文档/最终生成报告模版.md")
            if template_path.exists():
                with open(template_path, 'r', encoding='utf-8') as f:
                    template = f.read()
            else:
                template = self.get_default_template()

            current_date = datetime.now().strftime('%Y年%m月%d日')

            # 替换模板中的日期占位符
            template = template.replace('{current_date}', current_date)

            if self.openai_client:
                # 三段式生成 + 分段回退
                morning_prompt = self._build_morning_prompt(template, chart_insights, article_summary, current_date)
                morning = self.gemini_generate(morning_prompt) or (self._fallback_morning(chart_insights, article_summary, current_date))

                detailed_prompt = self._build_detailed_prompt(template, chart_insights, article_summary, current_date)
                detailed = self.gemini_generate(detailed_prompt) or (self._fallback_detailed(chart_insights, article_summary, current_date))

                appendix_prompt = self._build_appendix_prompt(template, chart_insights, article_summary, current_date)
                appendix = self.gemini_generate(appendix_prompt) or (self._fallback_appendix(article_summary, chart_insights))

                report = f"{morning}\n\n{detailed}\n\n{appendix}"
            else:
                report = self.generate_mock_report(current_date, chart_insights, article_summary)

            logger.info("✓ 最终报告生成完成")
            return report
        except Exception:
            logger.exception("生成最终报告失败")
            # 兜底：输出三段回退内容
            try:
                current_date = datetime.now().strftime('%Y年%m月%d日')
                morning = self._fallback_build_morning(agg or {}, current_date)
                detailed = self._fallback_build_detailed(agg or {})
                appendix = self._fallback_build_appendix(agg or {})
                return f"{morning}\n\n{detailed}\n\n{appendix}"
            except Exception:
                return self.generate_mock_report(
                    datetime.now().strftime('%Y年%m月%d日'),
                    chart_insights,
                    article_summary
                )
    # ---------- 三段式构建与回退 ----------
    def _build_morning_prompt(self, template: str, chart_insights: str, article_summary: str, current_date: str) -> str:
        return f"""
你是一位女性期权策略师（INTP），请仅输出模板中的【晨会口语分享稿】部分，严格遵循模板的段落与语气。

【日期】{current_date}
【图表分析洞察】
{chart_insights}
【舆情文章结构化摘要与聚合】
{article_summary}
【写作模板】
{template}
"""

    def _build_detailed_prompt(self, template: str, chart_insights: str, article_summary: str, current_date: str) -> str:
        return f"""
你是一位女性期权策略师（INTP），请仅输出模板中的【详细策略报告】部分，严格遵循模板的章节与字段，并保持数据口径一致。

【日期】{current_date}
【图表分析洞察】
{chart_insights}
【舆情文章结构化摘要与聚合】
{article_summary}
【写作模板】
{template}
"""

    def _build_appendix_prompt(self, template: str, chart_insights: str, article_summary: str, current_date: str) -> str:
        return f"""
你是一位女性期权策略师（INTP），请仅输出模板中的【附录：信息消化摘要】部分，列出图片与文本的“覆盖证明”。

【图表分析洞察】
{chart_insights}
【舆情文章结构化摘要与聚合】
{article_summary}
【写作模板】
{template}
"""

    def _fallback_morning(self, chart_insights: str, article_summary: str, current_date: str) -> str:
        return f"""
    # --------- 回退增强：用聚合数据生成可用内容 ---------
    def _fallback_build_morning(self, agg: dict, current_date: str) -> str:
        core = agg.get('core_narrative', '暂无')
        evid = agg.get('evidences', [])
        lines = ["### 【晨会口语分享稿】", f"（回退增强）日期：{current_date}", f"核心叙事：{core}", "主要证据链："]
        for i, e in enumerate(evid[:3], 1):
            lines.append(f"{i}. {e.get('来源','未知')}《{e.get('标题','未知')}》：{e.get('要点','')}")
        return "\n".join(lines)

    def _fallback_build_detailed(self, agg: dict) -> str:
        # 权重Top10表
        wt = agg.get('weights_table', [])[:10]
        table = ["| 类型 | 评分 | 来源 | 标题 | 理由 |", "|---|---|---|---|---|"]
        for r in wt:
            table.append(f"| {r.get('类型','')} | {r.get('评分','')} | {r.get('source','')} | {r.get('title','')} | {r.get('理由','')} |")

        # IV/HV/Skew 简表（从聚合数据中智能抽取）
        iv_table = self._extract_iv_hv_table(agg)

        risks = agg.get('risks', [])
        return "\n".join([
            "### 【详细策略报告】",
            "#### 一、核心叙事与证据链",
            f"- 核心叙事：{agg.get('core_narrative','暂无')}",
            "- 证据链：",
            *[f"  - {i+1}. {e.get('来源','未知')}《{e.get('标题','未知')}》：{e.get('要点','')}" for i, e in enumerate(agg.get('evidences', [])[:5])],
            "",
            "#### 二、权重评分Top10",
            *table,
            "",
            "#### 三、衍生品市场关键指标",
            *iv_table,
            "",
            "#### 四、风险摘录",
            *(f"- {r}" for r in risks) or ["- 暂无"],
        ])

    def _extract_iv_hv_table(self, agg: dict) -> list:
        # 从聚合数据中智能抽取IV/HV/Skew等关键指标
        import re

        # 从所有文本中搜索IV/HV/Skew相关数据
        all_text = ""
        for e in agg.get('evidences', []):
            all_text += e.get('要点', '') + " "
        for t in agg.get('appendix_texts', []):
            all_text += str(t) + " "

        # 正则模式匹配常见的期权指标
        patterns = {
            'IV': r'IV[：:]?\\s*(\\d+\\.?\\d*%?)',
            'HV': r'HV[：:]?\\s*(\\d+\\.?\\d*%?)',
            'Skew': r'(?:Skew|偏度)[：:]?\\s*(\\d+\\.?\\d*)',
            'PCR': r'PCR[：:]?\\s*(\\d+\\.?\\d*)',
            'IH': r'IH[：:]?\\s*(\\d+\\.?\\d*%?)',
            'IF': r'IF[：:]?\\s*(\\d+\\.?\\d*%?)',
            'IM': r'IM[：:]?\\s*(\\d+\\.?\\d*%?)',
        }

        extracted = dict()
        for key, pattern in patterns.items():
            matches = re.findall(pattern, all_text, re.IGNORECASE)
            if matches:
                extracted[key] = matches[0]

        # 构建表格
        if extracted:
            table = ["| 指标 | 数值 | 备注 |", "|---|---|---|"]
            for key, value in extracted.items():
                table.append("| " + key + " | " + value + " | 从文本中提取 |")
            return table
        else:
            return ["暂无可提取的IV/HV/Skew数据"]

    def _fallback_build_appendix(self, agg: dict) -> str:
        imgs = agg.get('appendix_images', [])[:10]
        texts = agg.get('appendix_texts', [])[:10]
        lines = ["### 【附录：信息消化摘要】", "- 图片清单（节选）："]
        lines += [f"  - {p}" for p in imgs] if imgs else ["  - 暂无"]
        lines += ["", "- 文本摘要（节选）："]
        lines += [f"  - {t}" for t in texts] if texts else ["  - 暂无"]
        return "\n".join(lines)

### 【晨会口语分享稿】
（回退版）基于聚合要点：
- 日期：{current_date}
- 核心叙事：见详细报告
- 提示：本段因网络原因未能由Gemini生成，已按模板骨架输出。
"""

    def _fallback_detailed(self, chart_insights: str, article_summary: str, current_date: str) -> str:
        return f"""
### 【详细策略报告】
（回退版）核心叙事与逻辑链、行情综述、宏观/资金面、衍生品（IV/HV/Skew）、策略与风控详见附录要点；建议在网络恢复后重生成。
"""

    def _fallback_appendix(self, article_summary: str, chart_insights: str) -> str:
        return f"""
### 【附录：信息消化摘要】
（回退版）
- 图表洞察：
{chart_insights}
- 文本要点：
{article_summary[:800]}
"""


    def ai_generate_report(self, template, chart_insights, article_summary, current_date):
        """兼容旧接口的保留方法（未使用）"""
        try:
            prompt = "此方法已被Gemini流程替代"
        except Exception:
            pass

    def _build_report_prompt(self, template: str, chart_insights: str, article_summary: str, current_date: str) -> str:
        # 采用你提供的Gemini报告风格模板作为生成提示
        return f"""
你是女性期权策略师（INTP），语言冷静、客观、精准，严禁比喻和夸张。基于以下信息，生成符合模板顺序与深度的报告：

【日期】{current_date}
【图表分析洞察】
{chart_insights}

【舆情文章结构化摘要与聚合】
{article_summary}

【写作模板（严格遵循顺序/结构/字段命名）】
{template}

要求：
- 先输出【晨会口语分享稿】，再输出【详细策略报告】，最后输出【附录：信息消化摘要】
- 明确给出：核心叙事、证据链、权重评分表、资金面结构、期权IV/HV/Skew要点、策略与风控
- 数字与指标必须与上文信息一致；对缺失数据使用“暂无”
"""


    def generate_mock_report(self, current_date, chart_insights, article_summary):
        """生成模拟报告"""
        return f"""
# 股指期货与期权晨会日报 ({current_date})

## 【晨会口语分享稿】

**(一、开场与市场概览，约1分钟)**
"行情方面，A股指数昨日全线收跌，上证指数下跌0.94%，深成指与创业板指跌幅均超过0.7%，市场呈现普跌格局。股指期货市场，三大主力合约同步下挫，且持仓量均出现下降。技术形态上，主要指数均已跌破关键短期均线，形成了破位下行的技术形态。"

**(二、关键驱动因素分析，约1.5分钟)**
"宏观因素方面，按重要性排序：
第一，国内强政策预期与弱现实的矛盾激化。7月PMI数据低于荣枯线，确认了经济基本面的收缩压力。
第二，美国非农数据爆冷，新增就业仅7.3万人，远低于预期，全球降息预期升温。
第三，期权市场波动率定价异常昂贵，为策略构建提供机会。"

**(三、衍生品市场深度论证，约1.5分钟)**
"期权市场显示，三大股指期权的IV-HV分位值依然处在70%以上的极端高位。这表明尽管指数已经下跌，但期权市场对未来更大波动的预期依然强烈，期权价格异常昂贵。这种定价结构为卖出波动率策略提供了极高的安全垫。"

**(四、策略建议，约1分钟)**
"建议构建熊市看跌价差策略，即买入平值看跌期权，同时卖出更虚值的看跌期权以降低成本。这样既能顺应下跌趋势，又能从高波动率中获利。"

**(五、风险提示，约30秒)**
"主要风险在于市场出现超预期反弹，特别是政策面的积极信号可能引发快速反弹。需密切关注指数在关键支撑位的表现。"

---

## 【详细策略报告】

### 🧭 一、核心叙事与逻辑链

**今日核心叙事**: 强预期退潮，市场进入风险释放与价值重估阶段。

**图表分析洞察**:
{chart_insights}

**舆情分析摘要**:
{article_summary}

### 🔍 二、行情概览与技术分析

市场整体呈现下跌格局，技术形态转弱，短期进入调整阶段。

### 🌐 三、宏观事件与资金面追踪

关键宏观事件包括PMI数据不及预期、美国非农数据爆冷等，资金面呈现净流出状态。

### 🧩 四、衍生品市场深度剖析

期权隐含波动率处于高位，PCR指标显示市场情绪偏向谨慎，为卖方策略提供机会。

### 🎯 五、今日交易策略与风控

**核心策略**: 熊市看跌价差策略
**风控要求**: 严格控制仓位，设置止损位

### ⚠️ 六、核心风险提示

1. 政策面超预期利好风险
2. 外围市场大幅波动风险
3. 流动性突然收紧风险

---

*报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}*
*免责声明: 本报告仅供参考，不构成投资建议*
        """

    def get_default_template(self):
        """获取默认模板"""
        return """
        # 股指期货与期权晨会日报模板

        ## 【晨会口语分享稿】
        ## 【详细策略报告】
        """

    def _format_aggregated_summary(self, agg: dict) -> str:
        lines = ["【核心叙事】" + agg.get("core_narrative", ""), "", "【主要证据链】"]
        for i, e in enumerate(agg.get("evidences", []), 1):
            lines.append(f"{i}. {e.get('来源','未知')}《{e.get('标题','未知')}》: {e.get('要点','')}")
        lines.append("")
        lines.append("【权重评分Top10】")
        for r in agg.get("weights_table", []):
            lines.append(f"- {r.get('类型')} {r.get('评分')}分 | {r.get('source')}《{r.get('title')}》: {r.get('理由','')}")
        lines.append("")
        lines.append("【风险摘录】" + "；".join(agg.get("risks", [])))
        lines.append("")
        lines.append("【附录-图片清单（节选）】")
        for t in agg.get("appendix_images", [])[:10]:
            lines.append(f"- {t}")
        lines.append("")
        lines.append("【附录-文本摘要（节选）】")
        for t in agg.get("appendix_texts", [])[:10]:
            lines.append(f"- {t}")
        return "\n".join(lines)

    def compose_report(self, chart_data, article_data):
        """编排生成完整报告（升级：逐文结构化分析 + 聚合 对齐V15.2）"""
        logger.info("开始编排生成完整报告（V15.2流程）...")

        try:
            # 1) 逐文结构化分析
            from modules.article_analyzer import ArticleAnalyzer
            from modules.analysis_aggregator import aggregate_summaries
            analyzer = ArticleAnalyzer(self.openai_client)

            summaries = []
            for article in (article_data.get('wechat') or []):
                try:
                    summaries.append(analyzer.analyze(article))
                except Exception as e:
                    logger.warning(f"单篇分析失败: {e}")

            # 2) 聚合为模板所需负载
            agg = aggregate_summaries(summaries)

            # 3) 把聚合结果拼入“舆情摘要”文本，供现有AI/Mock使用
            article_summary = self._format_aggregated_summary(agg)

            # 4) 整合图表洞察
            chart_insights = self.integrate_chart_insights(chart_data)

            # 5) 生成最终报告
            final_report = self.generate_final_report(chart_insights, article_summary, agg)

            # 6) 保存报告
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"daily_report_{timestamp}.md"
            output_path = get_output_path("report", filename)

            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(final_report)

            logger.info(f"✓ 完整报告已保存: {output_path}")

            return {
                "file_path": str(output_path),
                "filename": filename,
                "content_length": len(final_report),
                "generation_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

        except Exception as e:
            logger.error(f"报告编排失败: {e}")
            return None
